import {Component, EventEmitter, Input, OnD<PERSON>roy, OnInit, Output} from '@angular/core';
import {Session} from '../../../../shared/models/session.model';
import {Invoice} from '../../../../shared/models/invoice.model';
import {Appointment} from '../../../../shared/models/appointment.model';
import {SessionService} from '../../../../shared/services/session.service';
import {SocketService} from '../../../../core/services/socket.service';
import {Router} from '@angular/router';
import {NotificationService} from '../../../../shared/services/notification.service';
import {MatDialog} from '@angular/material/dialog';
import {StorageService} from '../../../../core/services/storage.service';
import {TranslateService} from '@ngx-translate/core';
import {InvoiceComponent} from '../../../../shared/components/invoice/invoice.component';
import {AppoitmentDialogComponent} from '../../../../shared/components/appoitment-dialog/appoitment-dialog.component';
import {Subscription} from "rxjs";
import {SupplyService} from "../../../../shared/services/supply.service";

@Component({
  selector: 'app-doctor-session',
  templateUrl: './doctor-session.component.html',
  styleUrls: ['./doctor-session.component.scss']
})
export class DoctorSessionComponent {
  @Output() sessionFinished: EventEmitter<any> = new EventEmitter<any>();

  @Input() session: Session;
  @Input() invoice: Invoice;
  @Input() patientAppointmentHistory: Appointment[];
  @Input() futurAppointment: Appointment;
  @Input() isArabicLanguageActive: boolean;


  public suppliesSuggestions: any[] = [];

  private getSuppliesSubscription: Subscription;


  constructor(
    private sessionService: SessionService,
    private socketService: SocketService,
    private router: Router,
    private notificationService: NotificationService,
    private dialog: MatDialog,
    private supplyService: SupplyService
  ) {

    this.getSupplies('');

  }





  endSession() {
    this.openInvoice();
  }

  getSupplies(searchText: string) {
    if (this.getSuppliesSubscription) {
      this.getSuppliesSubscription.unsubscribe();
    }
    this.getSuppliesSubscription = this.supplyService
      .getSupplies(searchText, 1, 1000) // TODO: Remove pagination
      .subscribe((res: any) => {
        this.suppliesSuggestions = res.docs;
      });
  }
  openInvoice() {
    const dialogRef = this.dialog.open(InvoiceComponent, {
      width: '800px',
      data: {
        invoice: this.invoice,
        session: this.session,
        suppliesSuggestions: this.suppliesSuggestions
      },
    });
    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        if (this.session?.appointment?._id) {
          this.sessionService
            .toAlmostCompleted(this.session?.appointment?._id)
            .subscribe((res) => {
              if (res) {
                this.sessionFinished.emit();

              }
            });
        }
      }
    });
  }
  futurAppointmentDialog() {
    this.dialog.open(AppoitmentDialogComponent, {
      width: '600px',
      data: {
        type: this.futurAppointment._id ? 'UPDATE' : 'CREATE',
        appointment: this.futurAppointment,
        fromSession: this.session._id,
      },
    });
  }

}
