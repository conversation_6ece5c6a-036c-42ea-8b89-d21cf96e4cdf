<full-calendar [events]="allAppointments"  #calendar [options]="calendarOptions" [deepChangeDetection]="true">
  <ng-template #eventContent let-arg>
    <app-appointment-calendar-item *ngIf="type === 'WEEKLY'" class="w-100" (appointmentClick)="handleAppointmentClick($event)" [appointment]="arg.event.extendedProps"></app-appointment-calendar-item>
  </ng-template>
  <ng-template #dayCellContent let-arg>
    <div *ngIf="type === 'MONTHLY'" class="day-number">{{arg.dayNumberText}}</div>
    <div class="day-cell-container" *ngIf="groupedArr[dateSuffix + ((arg.dayNumberText.length > 1 ? '' : '0') + arg.dayNumberText)]?.length">
      <div (click)="handleDayClick(arg.date)">{{groupedArr[dateSuffix + ((arg.dayNumberText.length > 1 ? '' : '0') + arg.dayNumberText)]?.length}}</div>
    </div>
  </ng-template>
</full-calendar>
