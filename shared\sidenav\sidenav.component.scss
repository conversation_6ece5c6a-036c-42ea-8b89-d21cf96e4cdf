@import '../../../../theming/variables';
@import '../../../../theming/shared.styles';
.sidenav-container {
  margin: 35px 20px 20px;
  transition: margin 300ms;
  height: calc(100% - 55px);

  .sidenav-logo {
    width: 200px;
    transition: opacity 150ms, width 300ms, height 600ms;
  }

  .list-icon-container {
    width: 100%;
    position: absolute;
    top: 0;
    background-color: $color-primary;
    padding: 2px;
    mat-icon {
      color: $color-light;
      cursor: pointer;
      font-weight: 500;
    }
  }
  app-seperator {
    width: 100%;
    margin-bottom: 20px;
  }
  app-labeled-avatar {
    margin-top: 20px;
    margin-bottom: 5px;
  }
  mat-selection-list {
    width: 100%;
    ::ng-deep .mat-list-item-content {
      padding: 0 !important;
      app-sidenav-item {
        height: 100%;
        margin: 1px
      }
    }
    ::ng-deep .mat-list-text {
      height: 100%;
      padding: 0 !important;
    }
  }
  .collapse {
    .sidenav-logo {
      width: 0;
      opacity: 0;
      height: 0;
    }
    app-labeled-avatar {
      opacity: 0;
      width: 0;
    }
  }

  .time-container{
    position: relative;
    margin-top: auto;
    text-align: center;
    font-size: 16px;
  }
}
.collapse {
  margin: 35px 0 0;
}
