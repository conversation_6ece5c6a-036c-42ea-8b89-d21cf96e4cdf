@import '../../../../theming/variables';
@import '../../../../theming/shared.styles';
.sidenav-container {
  margin: 35px 20px 20px;
  transition: margin 300ms;
  height: calc(100% - 55px);

  .sidenav-logo {
    width: 200px;
    transition: opacity 150ms, width 300ms, height 600ms;
  }

  .list-icon-container {
    width: 100%;
    position: absolute;
    top: 0;
    background-color: $color-primary;
    padding: 2px;
    mat-icon {
      color: $color-light;
      cursor: pointer;
      font-weight: 500;
    }
  }
  app-seperator {
    width: 100%;
    margin-bottom: 20px;
  }
  app-labeled-avatar {
    margin-top: 20px;
    margin-bottom: 5px;
  }
  mat-selection-list {
    width: 100%;
    ::ng-deep .mat-list-item-content {
      padding: 0 !important;
      app-sidenav-item {
        height: 100%;
        margin: 1px
      }
    }
    ::ng-deep .mat-list-text {
      height: 100%;
      padding: 0 !important;
    }
  }
  .collapse {
    .sidenav-logo {
      width: 0;
      opacity: 0;
      height: 0;
    }
    app-labeled-avatar {
      opacity: 0;
      width: 0;
    }
  }

  .secondary-nav-section {
    width: 100%;
    margin-top: auto;
    margin-bottom: 10px;

    app-seperator {
      width: 100%;
      margin-bottom: 10px;
    }

    .secondary-nav-item {
      display: flex;
      align-items: center;
      padding: 8px 16px;
      cursor: pointer;
      border-radius: 8px;
      transition: background-color 0.2s ease;
      color: $color-text-secondary;
      text-decoration: none;

      &:hover {
        background-color: rgba($color-primary, 0.1);
        color: $color-primary;
      }

      &.active {
        background-color: rgba($color-primary, 0.15);
        color: $color-primary;

        .secondary-nav-icon {
          color: $color-primary;
        }
      }

      .secondary-nav-icon {
        margin-right: 12px;
        font-size: 18px;
        width: 18px;
        height: 18px;
        color: inherit;

        [dir="rtl"] & {
          margin-right: 0;
          margin-left: 12px;
        }
      }

      .secondary-nav-text {
        font-size: 14px;
        font-weight: 400;
        color: inherit;
      }
    }
  }

  .time-container{
    position: relative;
    text-align: center;
    font-size: 16px;
  }
}
.collapse {
  margin: 35px 0 0;
}
