<div class="doctor-session-container">
  <div fxLayout fxLayoutGap="20px" fxLayoutAlign="space-between stretch">
    <mat-card
      class="patient-appointment-container h-100 g-b"
      fxLayoutAlign="start"
      fxLayout="column"
      fxFlex="70"
    >
      <app-patient-appointment-card
        [dir]="isArabicLanguageActive ? 'rtl' : 'ltr'"
        [isEditable]="true"
        [session]="session"
        [appointmentHistory]="patientAppointmentHistory"
        class="h-200px"
      ></app-patient-appointment-card>
    </mat-card>
    <mat-accordion class="example-headers-align" multi fxFlex="30">
      <div fxLayoutGap="8px" fxLayout="column">
        <app-custom-button (click)="endSession()">{{
          'currentSession.endSession' | translate
          }}</app-custom-button>
        <app-custom-button (click)="futurAppointmentDialog()">{{  
          'currentSession.scheduleFutureAppointment' | translate
          }}</app-custom-button>
        <app-chips-input [session]="session"></app-chips-input>
      </div>

      <mat-expansion-panel [expanded]="true">
        <mat-expansion-panel-header>
          <mat-panel-title>{{
            'currentSession.notes' | translate
            }}</mat-panel-title>
        </mat-expansion-panel-header>

        <app-notes-list
          [type]="'currentSession.note' | translate"
          [notes]="session.notes"
          [sessionID]="$any(session._id)"
        ></app-notes-list>
      </mat-expansion-panel>
      <mat-expansion-panel [expanded]="true">
        <mat-expansion-panel-header>
          <mat-panel-title>
            {{'currentSession.prescriptions.prescriptions' | translate}}
          </mat-panel-title>
        </mat-expansion-panel-header>
        <app-prescriptions [editable]="true" [prescriptions]="$any(session.prescriptions)" [session]="session"></app-prescriptions>
      </mat-expansion-panel>
    </mat-accordion>
  </div>
</div>
