import {
  Component,
  EventEmitter,
  Input,
  OnDestroy,
  OnInit,
  Output,
} from '@angular/core';
import { pages } from '../../config/pages';
import { Router } from '@angular/router';
import { NavigationService } from '../../../core/services/navigation.service';
import { AuthService } from '../../../core/services/auth.service';
import { StorageService } from '../../../core/services/storage.service';
import { User } from '../../models/user.model';
import { PROFILE_TYPES } from '../../constants/defaults.consts';
import { SocketService } from 'src/app/core/services/socket.service';
import { AppointmentService } from '../../services/appointment.service';
import { ThemePalette } from '@angular/material/core';
import { Direction } from '@angular/cdk/bidi';

@Component({
  selector: 'app-sidenav',
  templateUrl: './sidenav.component.html',
  styleUrls: ['./sidenav.component.scss'],
})
export class SidenavComponent implements OnInit, OnDestroy {
  @Input() fullSideNav = true;
  @Input() dir: Direction = 'ltr';

  @Output() sideNavModeChange = new EventEmitter();

  public connectedUser: User;

  public now: Date = new Date();
  
  public today: Date = new Date();

  


  constructor(
    private router: Router,
    private navigationService: NavigationService,
    private authService: AuthService,
    private storageService: StorageService,
    private appointmentService: AppointmentService,
    private socketService: SocketService
  ) {}

  public pages = [
    {
      pageTitle: 'pages.home',
      link: pages.home,
      icon: 'home-medic.svg',
      access: [PROFILE_TYPES.doctor],
    },
    {
      pageTitle: 'pages.appointments',
      link: pages.appointments,
      icon: 'appointment.svg',
      access: [PROFILE_TYPES.doctor, PROFILE_TYPES.assistant],
    },
    {
      id: 2,
      pageTitle: 'pages.currentSession',
      link: pages.doctor,
      icon: 'stethoscope.svg',
      access: [PROFILE_TYPES.doctor],
    },
    {
      pageTitle: 'pages.patients',
      link: pages.patients,
      icon: 'patient-male.svg',
      redirects: [],
      access: [PROFILE_TYPES.doctor, PROFILE_TYPES.assistant],
    },
    {
      pageTitle: 'pages.provisions',
      link: pages.supplies,
      icon: 'medical-supplies.svg',
      redirects: [],
      access: [PROFILE_TYPES.doctor, PROFILE_TYPES.assistant],
    },
    {
      pageTitle: 'pages.diagnosis',
      link: pages.diagnoses,
      icon: 'diagnoses.svg',
      redirects: [],
      access: [PROFILE_TYPES.doctor],
    },
    {
      pageTitle: 'pages.stats',
      link: pages.stats,
      icon: 'pie-chart.svg',
      redirects: [],
      access: [PROFILE_TYPES.doctor],
      onlyAdmin: true,
    },
    {
      pageTitle: 'pages.configuration',
      link: pages.config,
      icon: 'settings.svg',
      access: [PROFILE_TYPES.doctor],
      onlyAdmin: true,
    },
    {
      pageTitle: 'pages.clients',
      link: pages.clients,
      icon: 'costumer.svg',
      access: [PROFILE_TYPES.superAdmin],
    },
    {
      pageTitle: 'pages.configuration',
      link: pages.clientsConfigurations,
      icon: 'settings.svg',
      access: [PROFILE_TYPES.superAdmin],
    },
    {
      pageTitle: 'pages.termsOfUse',
      link: pages.termsOfUse,
      icon: 'description.svg',
      access: [PROFILE_TYPES.doctor, PROFILE_TYPES.assistant, PROFILE_TYPES.superAdmin],
    }
    // {
    //   pageTitle: 'Mon compte',
    //   link: pages.account,
    //   icon: 'user.svg',
    // },
  ];
  public patientAtOffice: number = 0;
  public inProgressAppointments: number = 0;
  ngOnInit(): void {
    this.setCurrentUser();
    this.getBadges();
    this.initSocketListner();

    setInterval(() => {
      this.now = new Date();
    }, 500);
  }
  getBadges() {
    this.appointmentService.doctorBadgeStat().subscribe((res) => {
      this.patientAtOffice = res.patientAtOffice;
      this.inProgressAppointments = res.inProgressAppointments;
    });
  }

  isSelected(pageLink: any): boolean {
    const pageLinkPeaces = pageLink?.split('/');
    const linkPeaces = this.router.url?.split('/');

    return (
      (linkPeaces.length === pageLinkPeaces.length &&
        pageLinkPeaces[linkPeaces.length - 1] ===
          linkPeaces[linkPeaces.length - 1]) ||
      (pageLinkPeaces[1] === 'account' && linkPeaces[1] === 'account')
    );
  }

  toggleSideNavModes(): void {
    this.sideNavModeChange.emit();
  }

  setCurrentUser() {
    this.connectedUser = this.storageService.getUser();
  }

  disconnect() {
    this.authService.logout();
    this.navigationService.navigateTo(pages.singIn);
  }

  isAllowed(page: any) {
    return page.access.includes(this.connectedUser?.profile?.title);
  }
  initSocketListner() {
    this.socketService.listen('header-update').subscribe((res: any) => {
      if (res) {
        this.getBadges();
      }
    });
  }
  ngOnDestroy(): void {
    this.socketService.removeAllListeners('header-update');
  }
  getBadgeNumber(route: string) {
    switch (route) {
      case pages.doctor: {
        return {
          number: this.inProgressAppointments,
          color: 'warn' as ThemePalette,
        };
      }
      /* case pages.appointments:{
        return {
          number:this.patientAtOffice,
          color:'warn'as ThemePalette
        }
      }*/
      default: {
        return {
          number: 0,
          color: 'warn' as ThemePalette,
        };
      }
    }
  }
}
