import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { DoctorComponent } from './doctor.component';
import { DoctorSummaryComponent } from './doctor-summary/doctor-summary.component';

const routes: Routes = [
  {
    path: '',
    component: DoctorComponent,
  },
  {
    path: 'summary',
    component: DoctorSummaryComponent,
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class DoctorRoutingModule {}
