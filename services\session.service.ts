import { Injectable } from '@angular/core';
import { SessionDal } from '../dals/session.dal';
import { Observable } from 'rxjs/internal/Observable';
import { Note } from '../models/note.model';
import { Prescription } from '../models/prescription.model';
import { Diagnose } from '../models/diagnose.model';
import { Invoice } from '../models/invoice.model';
import {map} from "rxjs/operators";
import {Session} from "../models/session.model";

@Injectable()
export class SessionService {
  constructor(private sessionDal: SessionDal) {}

  getCurrentSession(): Observable<any> {
    return this.sessionDal.getCurrentSession();
  }

  getHistorySession(
    patient: string,
    filters: any,
    page: number,
    limit: number
  ): Observable<any> {
    return this.sessionDal.getHistorySession(patient,filters, page, limit);
  }

  deleteSessionNote(noteID: string, sessionID: string): Observable<any> {
    return this.sessionDal.deleteSessionNote(noteID, sessionID);
  }

  updateSession(session: Session): Observable<any> {
    return this.sessionDal.updateSession(session);
  }

  addEditSessionNote(note: Note, sessionID: string): Observable<Note> {
    return this.sessionDal.addEditSessionNote(note, sessionID);
  }

  deleteSessionPrescription(
    prescriptionID: string,
    sessionID: string
  ): Observable<any> {
    return this.sessionDal.deleteSessionPrescription(prescriptionID, sessionID);
  }

  addEditSessionPrescription(
    prescription: Prescription,
    sessionID: string
  ): Observable<Prescription> {
    return this.sessionDal.addEditSessionPrescription(prescription, sessionID);
  }

  deleteSessionDiagnose(
    diagnoseID: string,
    sessionID: string
  ): Observable<any> {
    return this.sessionDal.deleteSessionDiagnose(diagnoseID, sessionID);
  }

  addEditSessionDiagnose(
    diagnose: Diagnose,
    sessionID: string
  ): Observable<Diagnose> {
    return this.sessionDal.addEditSessionDiagnose(diagnose, sessionID);
  }
  toAlmostCompleted(appointmentID: string) {
    return this.sessionDal.toAlmostCompleted(appointmentID);
  }

  createUpdateInvoice(invoice: Invoice) {
    return this.sessionDal.createUpdateInvoice(invoice);
  }
  getInvoice(sessionID: string) {
    return this.sessionDal.getInvoice(sessionID);
  }
  averageTime(date: string | undefined) {
    return this.sessionDal.averageTime(date);
  }
}
