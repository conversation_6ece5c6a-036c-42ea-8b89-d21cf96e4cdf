<div
  fxLayout="column"
  fxLayoutGap="10px"
  class="doctor-summary-container"
  *ngIf="!isLoading; else loader"
>
  <div fxLayoutGap="10px">
    <div
      fxFlex="70"
      class="card-container dynamic-shadow next-appointment-info-container"
    >
<!--        <app-patient-appointment-card-->
<!--          [dir]="isArabicLanguageActive ? 'rtl' : 'ltr'"-->
<!--          [appointment]="doctorViewInfo.nextAppointment"-->
<!--          [noHistory]="true"-->
<!--        ></app-patient-appointment-card>-->
        <h5>{{ 'doctorSummary.futureAppointments' | translate }}</h5>
        <div *ngFor="let futureAppointment of futureAppointments">
          <app-small-appointment
            [dir]="isArabicLanguageActive ? 'rtl' : 'ltr'"
            [appointment]="futureAppointment"
          ></app-small-appointment>
        </div>
        <div *ngIf="!futureAppointments || futureAppointments.length === 0">
          <app-no-results [isSmall]="true" [showImage]="false">
            {{ 'doctorSummary.noAppointmentsItemsInTheWaitingList' | translate }}
          </app-no-results>
        </div>
        <div
          (click)="goToAppoinments()"
          fxLayoutAlign="center center"
          class="view-more view-more-specific"
        >
          {{ 'doctorSummary.goToAppointments' | translate | uppercase }}
        </div>
      <ng-template #noAppointment>
        <div fxLayoutAlign="center" class="mt-3">
          <app-no-results *ngIf="!doctorViewInfo?.nextAppointment?._id"
            >{{ 'doctorSummary.noAppointmentsInTheWaitingList' | translate }}
          </app-no-results>
        </div>
      </ng-template>
    </div>
    <div fxLayout="column" fxFlex fxLayoutGap="10px">
      <div
        class="card-container dynamic-shadow waiting-people-container"
        fxLayoutAlign="space-around center"
        fxLayout="column"
        fxFlex
      >
        <span>{{ 'doctorSummary.peopleWaiting' | translate }}</span>
        <div
          class="person-image-container"
          fxLayout="row"
          fxLayoutAlign="center center"
        >
          <strong>{{ doctorViewInfo.patientsWaitingNumber }}</strong
          ><img src="assets/icons/person.png" alt="" />
        </div>
      </div>
      <div
        class="card-container dynamic-shadow waiting-time-container"
        fxLayoutAlign="space-around center"
        fxLayout="column"
        fxFlex
      >
        <span>{{ 'doctorSummary.averageWaitingTime' | translate }}</span>
        <mat-icon>schedule</mat-icon>
        <strong
          >{{ (doctorViewInfo.AvgWaitingTime || 0).toFixed(0) }}
          {{ 'general.minuteShort' | translate }}</strong
        >
      </div>
    </div>
  </div>
  <div
    class="card-container dynamic-shadow delay-options-container"
    fxLayoutAlign="space-around"
  >
    <button
      mat-raised-button
      color="primary"
      *ngFor="let delayOption of delayOptions"
      (click)="delayClick(delayOption)"
    >
      {{ delayOption }} {{ 'general.minuteShort' | translate }}
    </button>
    <div fxLayoutGap="5px" class="custom-delay-container">
      <input #otherDelayOption type="number" placeholder="Ex. 6" />
      <button
        mat-raised-button
        color="primary"
        (click)="delayClick(+otherDelayOption.value)"
      >
        {{ 'doctorSummary.validate' | translate }}
      </button>
    </div>
  </div>
<!--  <div-->
<!--    class="card-container dynamic-shadow p-2 mb-3"-->
<!--    fxLayout="column"-->
<!--    fxLayoutAlign="space-around"-->
<!--  >-->
<!--    <h5>{{ 'doctorSummary.futureAppointments' | translate }}</h5>-->
<!--    <div *ngFor="let futureAppointment of futureAppointments">-->
<!--      <app-small-appointment-->
<!--        [dir]="isArabicLanguageActive ? 'rtl' : 'ltr'"-->
<!--        [appointment]="futureAppointment"-->
<!--      ></app-small-appointment>-->
<!--    </div>-->
<!--    <div *ngIf="!futureAppointments || futureAppointments.length === 0">-->
<!--      <app-no-results [isSmall]="true" [showImage]="false">-->
<!--        {{ 'doctorSummary.noAppointmentsItemsInTheWaitingList' | translate }}-->
<!--      </app-no-results>-->
<!--    </div>-->
<!--    <div-->
<!--      (click)="goToAppoinments()"-->
<!--      fxLayoutAlign="center center"-->
<!--      class="view-more"-->
<!--    >-->
<!--      {{ 'doctorSummary.goToAppointments' | translate | uppercase }}-->
<!--    </div>-->
<!--  </div>-->
  <div
    class="card-container dynamic-shadow p-2 mb-3"
    fxLayout="column"
    fxLayoutAlign="space-around"
  >
    <div fxLayout="row" fxLayoutAlign="space-between center">
      <div fxFlex fxLayoutAlign="start center">
        <h5 class="mb-0">
          {{ 'doctorSummary.breaksAndUnavailability' | translate }}
        </h5>
      </div>
      <div fxFlex="40" fxLayoutAlign="center center">
        <app-date-picker
          [dir]="isArabicLanguageActive ? 'rtl' : 'ltr'"
          [day]="dateForTimeOffs"
          (SelectedDay)="manageDaySelection($event)"
          (dateSwitch)="onDayChange($event)"
        ></app-date-picker>
      </div>
      <app-select-doctor
        [doctors]="allowedDoctors"
        [selectedDoctors]="doctors"
        (doctorIDsChangeEvent)="selectDoctorChange($event)"
      ></app-select-doctor>

      <div fxFlex fxLayoutAlign="end center">
        <app-circle-button
          name="add"
          [matTooltip]="'doctorSummary.tooltips.addBreak' | translate"
          (click)="createTimeoffClick()"
        ></app-circle-button>
      </div>
    </div>
    <div *ngFor="let timeoff of timeoffs">
      <app-timeoff
        [dir]="isArabicLanguageActive ? 'rtl' : 'ltr'"
        [allowEdit]="true"
        [timeoff]="timeoff"
        (timeoffUpdatedEvent)="manageUpdateTimeoff($event)"
      ></app-timeoff>
    </div>
    <div *ngIf="!timeoffs || timeoffs.length === 0">
      <app-no-results [isSmall]="true" [showImage]="false">
        AUCUN PAUSE OU INDISPONIBILITÉ N'EST DECLARÉ
      </app-no-results>
    </div>
  </div>
</div>
<ng-template #loader>
  <app-session *ngIf="isLoading"></app-session>
</ng-template>
