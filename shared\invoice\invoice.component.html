<div
  class="position-relative"
  [ngClass]="{ 'arabic-settings': isArabic }"
  [dir]="dir"
>
  <button
    mat-icon-button
    class="close-button close-button-big"
    (click)="closeInvoice($event)"
  >
    <mat-icon class="close-icon" color="warn">close</mat-icon>
  </button>
  <div class="content-container scrollbar-primary">
    <div class="header">
      <div class="logo" fxLayoutAlign="center">
        <img src="assets/logos/logo-icon.png" />
      </div>
      <h2 fxLayoutAlign="center" class="hospital-name gradient-bg">
        {{ currentUser?.profile?.hospital?.name }}
      </h2>
      <div class="big-mb" fxLayout="row" fxLayoutAlign="space-between">
        <div class="appointment-info">
          <div>
            <div>{{ 'invoice.mr' | translate }}</div>
            {{
              invoice?.session?.patient?.lastName +
                ' ' +
              invoice?.session?.patient?.firstName
            }}
          </div>
          <div>
            <div>{{ 'invoice.type' | translate }}</div>
            {{ 'CONSULTATION' }}
          </div>
        </div>
        <div class="appointment-info">
          <div>
            <div>{{ 'invoice.doctor' | translate }}</div>
            {{
              currentUser.profile?.lastName +
                ' ' +
                currentUser.profile?.firstName
            }}
          </div>
          <div>
            <div>{{ 'invoice.date' | translate }}</div>
            {{ '07/03/2021' }}
          </div>
        </div>
      </div>
    </div>

    <table class="table table-striped">
      <thead>
        <tr class="gradient-bg">
          <th scope="col" colspan="2">{{ 'invoice.name' | translate }}</th>
          <th scope="col" class="text-align-end" colspan="2">
            {{ 'invoice.price' | translate }}
          </th>
          <th scope="col" class="text-align-end" colspan="2">
            {{ 'invoice.quantity' | translate }}
          </th>
          <th scope="col" class="text-align-end" colspan="2">
            {{ 'invoice.total' | translate }}
          </th>
          <th scope="col"></th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let item of invoice.items; let i = index">
          <th scope="row" colspan="2">
<!--            <input-->
<!--              (keyup)="inputChange(item, $event)"-->
<!--              matInput-->
<!--              [value]="item.name"-->
<!--              class="price-input"-->
<!--              [matAutocomplete]="auto"-->
<!--            />-->
<!--            <mat-autocomplete-->
<!--              (optionSelected)="setItem($event, i)"-->
<!--              #auto="matAutocomplete"-->
<!--            >-->
<!--              <mat-option-->
<!--                *ngFor="let suppliesSuggestion of suppliesSuggestions"-->
<!--                [value]="suppliesSuggestion"-->
<!--              >-->
<!--                {{ suppliesSuggestion.name }}-->
<!--              </mat-option>-->
<!--            </mat-autocomplete>-->
            <mat-form-field>
              <mat-select [value]="item" (selectionChange)="setItem($event, i)" [compareWith]="compareFn">
                <mat-option>
                  <ngx-mat-select-search (keyup)="inputChange(item, $event)"></ngx-mat-select-search>
                </mat-option>
                <mat-option [value]="null"></mat-option>
                <mat-option *ngFor="let suppliesSuggestion of suppliesSuggestions" [value]="suppliesSuggestion">
                  {{suppliesSuggestion.name}}
                </mat-option>
              </mat-select>
            </mat-form-field>
          </th>


          <td colspan="2" class="text-align-start">
            <!--        {{item.costPrice}} DH-->
            {{ item.price }}
            {{ '' | currencyShort | translate }}
            <!-- <input
              matInput
              type="number"
              [style]="'width: ' + getWidth(item.price) + 'em'"
              [(ngModel)]="item.price"
              (ngModelChange)="onPriceChange($event, i)"
              class="price-input"
            /> -->
            
          </td>
          <td colspan="2" class="text-align-end">
            <input
              type="number"
              matInput
              [style]="'width: ' + getWidth(item.quantity) + 'em'"
              [(ngModel)]="item.quantity"
              (ngModelChange)="onQuantityChange($event, i)"
              class="price-input"
            />
          </td>
          <td colspan="2" class="text-align-end">
            {{ getItemTotal(item.price, item.quantity)
            }}{{ '' | currencyShort | translate }}
          </td>
          <td>
            <mat-icon *ngIf="i !== 0" class="remove-icon" (click)="removeItem(i)"
              >delete
            </mat-icon>
          </td>
        </tr>
        <tr>
          <td [dir]="dir" colspan="6" class="total-bold">
            {{ 'invoice.finalTotal' | translate }}
          </td>
          <td colspan="2" class="text-align-end total-bold">
            {{ getTotal() }}{{ '' | currencyShort | translate }}
          </td>
          <div class="position-relative">
            <div class="add-icon" (click)="addItem()">
              <img src="assets/icons/plus.svg" />
            </div>
          </div>
        </tr>
      </tbody>
    </table>
  </div>
</div>
<app-invoice-print
  *ngIf="invoice"
  [appointment]="invoice.session?.appointment"
  [invoice]="invoice"
></app-invoice-print>

<div fxLayout="row" fxLayoutGap="5px" class="mt-1">
  <app-custom-button
    [loading]="isLoadingPDF"
    fxFlex
    color="accent"
    (click)="exportAsPDF()"
  >
    <div fxLayoutAlign="center center">
      {{ 'invoice.downloadPdf' | translate }}
      <mat-icon>download</mat-icon>
    </div>
  </app-custom-button>
  <app-custom-button fxFlex (click)="createUpdateInvoice()"
    >{{ (invoice.session?.appointment.state !== APPOINTMENT_STATES_OBJECT.almostCompleted ? 'invoice.endSession' : 'invoice.endSession2') | translate }}
  </app-custom-button>
</div>
