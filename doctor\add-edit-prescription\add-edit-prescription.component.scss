@import '../../../../../theming/variables';
@import '../../../../../theming/shared.styles';


.add-edit-prescription-dialog-container {
  position: relative;

  .dialog-content-container {
    display: flex;

    .prescription-items {
      flex:3;
      .prescription-name {
        width: 100%;
      }

      .prescription-selected-items {
        height: 400px;
        margin-bottom: 20px;
        .prescription-selected-item {
          .inputs-manager {
            display: flex;
            justify-content: space-between;
          }
          .item-name {
            font-weight: 600;
            color: $color-primary;
            text-decoration: underline;
            img {
              width: 20px;
              margin-right: 5px;
            }
            .delete-item-icon {
              font-size: 14px;
              margin-left: 14px;
              margin-top: 5px;
              cursor: pointer;
            }
            .custom-name-input {
              border: none;
              color: $color-primary;
            }
          }

          .custom-quantity-input {
            width: 60px;
            text-align: right;
            color: $color-primary;
          }
          .item-notes {
            margin-left: 25px;
            list-style-type: circle;
            .minus-icon {
              width: 10px;
              margin-right: 5px;
              cursor: pointer;
            }
            .item-note {
              display: flex;
              align-items: center;
              margin-bottom: 3px;
              margin-top: 3px;
              .note-input {
                max-width: 80%;
                resize: none;
                overflow: hidden;
                border: none;
              }
            }
            .add-line {
              cursor: pointer;
              color: darken($primary,10);
              font-size: 10px;
              .add-line-icon {
                width: 10px;
                margin-right: 5px;
              }
            }
            .add-line:hover {
              color: darken($primary,20);
            }
          }
        }

      }

      .divider {
        margin-bottom: 10px;
      }
      .selected-elements-footer {
        display: flex;
        align-items: end;
        justify-content: space-between;

        .total-container {
          font-family: sans-serif;
          font-weight: 600;
          margin-bottom: 10px;
          margin-right: 20px;
        }
      }
      .action-buttons {
        display: flex;
        justify-content: space-around;
        app-custom-button {
          width: 200px;
        }
      }
    }
    mat-divider {
      margin-right: 10px;
      margin-left: 10px;
    }
    .prescription-selection {
      overflow: scroll;
      max-height: 500px;
      flex:2;
      .search-items {
        width: 100%;
      }
      .loading-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 40px 20px;
        height: 200px;

        mat-spinner {
          margin-bottom: 16px;
        }

        p {
          color: $color-primary;
          font-size: 14px;
          margin: 0;
          text-align: center;
        }
      }

      .prescription-search-items {
        max-height: 400px;
        .prescription-search-item {
          .family-name {
            font-weight: 600;
            color: $color-primary;
            text-decoration: underline;
            img {
              width: 20px;
              margin-right: 5px;
            }
          }
          .family-items {
            margin-left: 25px;
            .minus-icon {
              width: 10px;
              margin-right: 5px;
              cursor: pointer;
            }
            .family-item {
              color: $color-secondary;
              cursor: pointer;
              display: flex;
              align-items: center;
              margin-bottom: 3px;
              margin-top: 3px;
              .note-input {
                border: none;
              }
            }
            .add-line {
              cursor: pointer;
              color: darken($primary,10);
              .add-line-icon {
                width: 14px;
                margin-left: -16px;
                margin-right: 5px;
              }
            }
            .add-line:hover {
              color: darken($primary,20);
            }
          }
        }
      }
    }



  }
  .add-custom-button {
    margin-bottom: 10px;
    margin-left: 20px;
    div {
      display: flex;
      align-items: center;
      mat-icon {
      }
      >div {
        display: flex;
        flex-direction: column;
        align-items: start;
        .main-text {
        }
        .sub-text {
          font-size: 10px;
          line-height: 12px;
          margin-top: -5px;
          margin-bottom: 10px;

        }
      }
    }
  }
  .bottom-button {
    position: absolute;
    bottom: 0;
    right: 0;
  }

}
