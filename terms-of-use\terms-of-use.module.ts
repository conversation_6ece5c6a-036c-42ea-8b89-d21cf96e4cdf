import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';

import { TermsOfUseRoutingModule } from './terms-of-use-routing.module';
import { TermsOfUseComponent } from './terms-of-use.component';

@NgModule({
  declarations: [
    TermsOfUseComponent
  ],
  imports: [
    CommonModule,
    TermsOfUseRoutingModule,
    TranslateModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule
  ]
})
export class TermsOfUseModule { }
