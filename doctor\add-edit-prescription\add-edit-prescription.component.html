<div class="add-edit-prescription-dialog-container">
  <div mat-dialog-title fxLayoutAlign="center center">
    <div class="mb-0 text-uppercase">
      {{ data?.editable ? (action === 'EDIT' ? 'Modifier' : 'Ajouter') : '' }} {{ prescriptionType }}
    </div>
    <button mat-icon-button class="close-button" [mat-dialog-close]="true">
      <mat-icon class="close-icon" color="warn">close</mat-icon>
    </button>
  </div>
  <div class="dialog-content-container">
    <div class="prescription-items">
      <mat-form-field
        appearance="legacy"
        class="prescription-name"
        [class.mat-form-field-invalid]="isNotValid"
      >
        <mat-label>Nom*</mat-label>
        <input
          matInput
          [(ngModel)]="prescriptionPage.title"
          [placeholder]="'general.searchPlaceHolder' | translate"
        />
        <mat-hint class="mat-error" *ngIf="isNotValid"
          >Nom de feuille est necessaire</mat-hint
        >
      </mat-form-field>
      <div class="prescription-selected-items scrollbar scrollbar-primary">
        <div
          class="prescription-selected-item"
          *ngFor="
            let selectedPresc of $any(prescriptionPage?.items);
            let itemIndex = index
          "
        >
          <div class="inputs-manager">
            <span class="item-name font-weight-bold">
              <img src="assets/icons/right-arrow.svg" />
              <span *ngIf="selectedPresc[presName]">{{
                selectedPresc.name
              }}</span>
              <span *ngIf="!selectedPresc[presName] && prescriptionType !== 'AUTRE'" >
                <input
                  class="custom-name-input"
                  placeholder="Inserer le nom"
                  [(ngModel)]="selectedPresc.name"
                  [id]="'presc-' + itemIndex"
                  appAutowidth
                />
              </span>
              <span *ngIf="!selectedPresc[presName] && prescriptionType === 'AUTRE'" style="min-width: 100px" >

                <span style="text-align: justify" #spInput class="input" role="textbox" contenteditable (focusout)="editText(spInput, selectedPresc, $any(spInput).textContent)" [id]="'presc-' + itemIndex">{{selectedPresc?.name}}</span>
              </span>
              <mat-icon
                *ngIf="data?.editable"
                (click)="deletePrescriptionItem(itemIndex)"
                class="delete-item-icon"
                color="accent"
                aria-hidden="false"
                aria-label="Example home icon"
                >close</mat-icon
              ></span
            >
            <div *ngIf="prescriptionType === 'ORDONNANCE'">
              <input
                type="number"
                class="custom-quantity-input"
                placeholder="Prix"
                [disabled]="!data?.editable"
                [(ngModel)]="selectedPresc.price"
                matInput
                [min]="0"
              />
              <span> DH x </span>
              <input
                type="number"
                class="custom-quantity-input"
                placeholder="Qte"
                [disabled]="!data?.editable"
                matInput
                [min]="0"
                [(ngModel)]="selectedPresc.quantity"
              />
            </div>
          </div>
          <div class="item-notes" *ngIf="selectedPresc">
            <div
              class="item-note"
              *ngFor="
                let note of selectedPresc.notes;
                let i = index;
                trackBy: trackByFn
              "
            >
              <img
                class="minus-icon"
                src="assets/icons/delete-button.svg"
                (click)="deleteNote(itemIndex, i)"
                *ngIf="data?.editable"
              />
                <textarea
                  class="note-input"
                  [mat-autosize]="true"
                  *ngIf="selectedPresc.notes"
                  [disabled]="!data?.editable"
                  placeholder="Ajouter des details"
                  [id]="'item-' + itemIndex + '-' + i"
                  (keydown.enter)="addNote(itemIndex, $event)"
                  [value]="selectedPresc.notes[i]"
                  (change)="handleNoteChange(itemIndex, i, $event)"
              ></textarea>
            </div>
            <span class="add-line" (click)="addNote(itemIndex)" *ngIf="data?.editable && prescriptionType !== 'AUTRE'"
              ><img class="add-line-icon" src="assets/icons/add.svg" />Ajouter
              une ligne</span>
          </div>
        </div>
      </div>
      <div class="selected-elements-footer">
        <button
          mat-raised-button
          color="primary"
          class="add-custom-button"
          *ngIf="data?.editable"
          (click)="addCustomElement()"
        >
          <div>
            <mat-icon matSuffix>add</mat-icon>
            <div>
              <span class="main-text">Ajouter</span>
              <span class="sub-text">Element personalise</span>
            </div>
          </div>
        </button>
        <div class="total-container"><span *ngIf="prescriptionType === 'ORDONNANCE'">Total: {{ total | number: '1.2-2'}} DH</span></div>
      </div>

      <mat-divider class="divider"></mat-divider>
      <div class="action-buttons">
        <app-custom-button
          (buttonClick)="handleSubmit()"
          [disabled]="isNotValid"
          *ngIf="data?.editable"
          >{{ 'Terminer' }}</app-custom-button
        >
        <app-custom-button [color]="undefined" (buttonClick)="handlePrint()">{{
          'Imprimer'
        }}</app-custom-button>
      </div>
    </div>
    <mat-divider [vertical]="true" *ngIf="hasSelection && data?.editable"></mat-divider>
    <div class="prescription-selection" *ngIf="hasSelection && data?.editable">
      <mat-form-field appearance="legacy" class="search-items">
        <mat-label>{{ 'appointments.search' | translate }}</mat-label>
        <input
          (input)="handleSearch($event)"
          matInput
          [placeholder]="'general.searchPlaceHolder' | translate"
        />
        <mat-icon matSuffix>search</mat-icon>
      </mat-form-field>

      <!-- Loading spinner for drugs -->
      <div class="loading-container" *ngIf="isLoadingDrugs && prescriptionType === 'ORDONNANCE'">
        <mat-spinner diameter="40"></mat-spinner>
        <p>Chargement des médicaments...</p>
      </div>

      <div class="prescription-search-items scrollbar scrollbar-primary" *ngIf="isGroupable && !isLoadingDrugs">
        <div
          class="prescription-search-item"
          *ngFor="let item of groupedPrescriptionData"
        >
          <div *ngIf="$any(item.name) != 0">
            <span class="family-name font-weight-bold"
              ><img src="assets/icons/right-arrow.svg" /> {{ item.name }}</span
            >
            <div class="family-items">
              <div
                class="family-item"
                (click)="addPresc(presc)"
                *ngFor="let presc of item.items"
              >
                <span>+ {{ presc.name }}</span>
              </div>
            </div>
          </div>
          <div *ngIf="$any(item.name) == 0">
            <div
              class="prescription-search-item"
              *ngFor="let presc of item.items"
            >
              <span
                class="family-name font-weight-bold pointer"
                (click)="addPresc(presc)"
                >+ {{ presc.name }}</span
              >
            </div>
          </div>
        </div>
      </div>
      <div class="prescription-search-items" *ngIf="!isGroupable && !isLoadingDrugs">
        <div
          class="prescription-search-item"
          *ngFor="let presc of selectionData"
        >
          <span
            class="family-name font-weight-bold pointer"
            (click)="addPresc(presc)"
            >+ {{ presc.name }}</span
          >
        </div>
      </div>
      <button
        *ngIf="hasSelection"
        mat-raised-button
        color="accent"
        class="add-custom-button bottom-button"
        (click)="addProduct()"
      >
        <div>
          <mat-icon matSuffix>add</mat-icon>
          <div>
            <span class="main-text">Ajouter</span>
            <span class="sub-text">Un produit {{ addSuffix }}</span>
          </div>
        </div>
      </button>
    </div>
  </div>
</div>
