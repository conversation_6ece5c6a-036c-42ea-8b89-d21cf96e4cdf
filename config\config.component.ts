import { Component, OnInit, ViewChild } from '@angular/core';
import { StorageService } from 'src/app/core/services/storage.service';
import * as moment from 'moment';
import { Hospital } from 'src/app/shared/models/hospital.model';
import { Direction } from '@angular/cdk/bidi';
import { TranslateService } from '@ngx-translate/core';
import { MatTabChangeEvent } from '@angular/material/tabs';
import {
  BaseComponentCanDeactivateDirective
} from '../../../shared/components/base-candeactive/base-candeactivate.directive';

@Component({
  selector: 'app-config',
  templateUrl: './config.component.html',
  styleUrls: ['./config.component.scss'],
})
export class ConfigComponent extends BaseComponentCanDeactivateDirective implements OnInit {

  @ViewChild('hospitalComponent') hospitalComponent: any;
  @ViewChild('timeoffsComponent') timeoffsComponent: any;
  @ViewChild('staffComponent') staffComponent: any;
  @ViewChild('otherConfigsComponent') otherConfigsComponent: any;

  public hospital: Hospital;
  public dir: Direction = 'ltr';

  unsaved = false;
  currentTabIndex = 0;
  isSaving = false;
  showSaveButton = true;
  hasUnsavedChanges = false;
  isFormValid = true;

  constructor(
    private storageService: StorageService,
    private translate: TranslateService
  ) {

    super();
    this.dir = translate.currentLang === 'ar' ? 'rtl' : 'ltr';
    translate.onLangChange.subscribe(() => {
      this.dir = translate.currentLang === 'ar' ? 'rtl' : 'ltr';
    });
  }

  ngOnInit(): void {
    const user = this.storageService.getUser();
    if (user && user.profile && user.profile.hospital) {
      this.hospital = user.profile.hospital;
    }
  }

  hasUnsavedData(): boolean {
    return this.unsaved;
  }

  handleSaveChange(saved: boolean) {
    this.unsaved = !saved;
    this.hasUnsavedChanges = !saved;
    this.updateFormValidity();
    console.log('Save status changed:', { saved, hasUnsavedChanges: this.hasUnsavedChanges, isFormValid: this.isFormValid });
  }

  handleValidityChange(isValid: boolean) {
    this.isFormValid = isValid;
    console.log('Form validity changed:', { isValid, hasUnsavedChanges: this.hasUnsavedChanges });
  }

  updateFormValidity() {
    // Check form validity based on current tab
    switch (this.currentTabIndex) {
      case 0: // Cabinet/Hospital tab
        this.isFormValid = this.hospitalComponent?.isValidForm() ?? true;
        break;
      case 1: // Breaks tab
        this.isFormValid = true; // Timeoffs tab doesn't have strict validation requirements
        break;
      case 2: // Team tab
        this.isFormValid = true; // Staff tab doesn't have strict validation requirements
        break;
      case 3: // Others tab
        this.isFormValid = true; // Other configs tab doesn't have strict validation requirements
        break;
      default:
        this.isFormValid = true;
        break;
    }
    console.log('Form validity updated for tab', this.currentTabIndex, ':', this.isFormValid);
  }

  onTabChange(event: MatTabChangeEvent) {
    console.log('Tab changed to index:', event.index);
    this.currentTabIndex = event.index;

    // Reset change status when switching tabs
    this.hasUnsavedChanges = false;

    // Update save button visibility based on tab
    this.updateSaveButtonVisibility();
    this.updateFormValidity();

    console.log('After tab change - showSaveButton:', this.showSaveButton, 'hasUnsavedChanges:', this.hasUnsavedChanges);
  }

  updateSaveButtonVisibility() {
    // Show save button for all tabs
    this.showSaveButton = true;
    console.log('Save button visibility updated for tab', this.currentTabIndex, ':', this.showSaveButton);
  }

  saveCurrentTab() {
    console.log('Saving current tab:', this.currentTabIndex);
    this.isSaving = true;

    switch (this.currentTabIndex) {
      case 0: // Cabinet/Hospital tab
        console.log('Saving hospital component');
        if (this.hospitalComponent && this.hospitalComponent.formSubmit) {
          this.hospitalComponent.formSubmit();
        }
        break;
      case 1: // Breaks tab
        console.log('Saving timeoffs component');
        if (this.timeoffsComponent && this.timeoffsComponent.save) {
          this.timeoffsComponent.save();
        } else {
          // Timeoffs saves automatically, so just mark as saved
          this.handleSaveChange(true);
        }
        break;
      case 2: // Team tab
        console.log('Saving staff component');
        if (this.staffComponent && this.staffComponent.save) {
          this.staffComponent.save();
        } else {
          // Staff saves automatically, so just mark as saved
          this.handleSaveChange(true);
        }
        break;
      case 3: // Others tab
        console.log('Saving other configs component');
        if (this.otherConfigsComponent && this.otherConfigsComponent.save) {
          this.otherConfigsComponent.save();
        } else {
          // Other configs save automatically, so just mark as saved
          this.handleSaveChange(true);
        }
        break;
    }

    setTimeout(() => {
      this.isSaving = false;
      console.log('Save operation completed');
    }, 1000);
  }
}
