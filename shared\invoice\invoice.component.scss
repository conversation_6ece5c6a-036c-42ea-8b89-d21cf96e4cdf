@import '../../../../theming/variables';


#invoice-container {
  position: relative;
  height: 1080px;
}
.invoice-container {
  position: relative;
  height: 1080px;
}

.pdf-header {
  position: absolute;
  top: 50px;
}

.content-container {
  max-height: 80vh;
  padding: 30px;
  border: 1px solid $color-primary;
  overflow-y: scroll;
  overflow-x: hidden;
}

.pdf-table {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.header {
  .logo {
    img {
      width: 70px;
      height: 70px;
    }

  }

  .hospital-name {
    margin-top: 5px;
    color: white;
    border-radius: 3px;
  }

  .appointment-info {
    margin-bottom: 20px;

    > div {
      display: flex;

      > div {
        width: 80px;
        color: $color-grey;
      }
    }
  }

}

.price-input {
}

.add-icon {
  position: absolute;
  right: -17px;
  top: -17px;
  cursor: pointer;

  img {
    height: 34px;
    width: 34px;
  }
}

.remove-icon {
  cursor: pointer;
}

.remove-icon:hover {
  color: $color-warn;
}

.gradient-bg {
  background: #36D1DC; /* fallback for old browsers */
  background: -webkit-linear-gradient(to right, #5B86E5, #36D1DC); /* Chrome 10-25, Safari 5.1-6 */
  background: linear-gradient(to right, #5B86E5, darken(#36D1DC, 10)); /* W3C, IE 10+/ Edge, Firefox 16+, Chrome 26+, Opera 12+, Safari 7+ */
}

.big-mb {
  margin-bottom: 150px;
}

.text-align-end {
  text-align: start;
  direction: RTL
}

.total-bold {
  font-family: Roboto, serif;
  font-weight: bold;
  font-size: 16px;
}

.total-bold[dir="rtl"] {
  font-family: Cairo, Roboto, serif;
}
