@import "../../../../../theming/variables";

.sidenav-item-container {
  height: 100%;
  border-radius: 4px;

  .badge-container[dir="rtl"] {
    .mat-badge-content {
      text-align: center;
    }
  }

  .icon-container {
    width: 40px;
    height: 40px;
    margin-left: 5px;

    img {
      width: 25px;
      height: 25px;
    }
  }

  .selected-icon {
    background-color: $color-light;
    border-radius: 50%;
  }

  .item-content {
    max-width: 200px;
    margin-left: 5px;
    transition: all 0.3s linear;
  }
}

.sidenav-item-container[dir="rtl"] {
  .icon-container {
    margin-right: 5px;
    margin-left: 0;
  }

  .item-content {
    margin-right: 5px;
    margin-left: 0;
  }
}

.selected {
  background-color: $color-primary;
  color: $color-light
}

.collapse {
  border-radius: 0;

  .item-content {
    visibility: hidden;
    width: 0;
    max-width: 0;
  }
}
