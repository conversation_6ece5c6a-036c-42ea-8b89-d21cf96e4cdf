@import "../../../../theming/variables";
mat-button-toggle-group {
  height: 50px;
}
.mat-button-toggle-checked {
  background-color: $color-primary;
  color: $color-light;
}

.appointments-page-container {
  position: relative;
  height: calc(100vh - 100px);
  overflow-y: scroll;
  .content-section {
  }
  .options-bar-container {
    .first-line-option {
      border: 1px $color-light-grey solid;
      height: 60px;
      display: flex;
      align-items: center;
    }
    .mat-form-field {
      width: 60%;
    }
    .click-options {
      app-text-check-toggle {
        margin-right: 10px;
      }
      text-align: end;
    }
  }
  .date-picker-container {
    display: flex;
    justify-content: center;
  }
  .app-separator {
    margin-top: 5px;
  }
}

.item-icon {
  width: 40px;
  height: 40px;
  border-color: unset;
  background-color: unset;
}
.button-icon {
  width: 40px;
  height: 40px;
  border-color: unset;
  background-color: unset;

}
.sep-pause {
  background: #f5f5f5;
  border: 1px solid #dfdfdf;
  justify-content: center;
  padding: .6rem 0;
  margin-top: 1.5rem;
  margin-bottom: 1.5rem;
  border-radius: 5px
}

.sep-pause h4 {
  text-align: center;
  margin-bottom: 0;
  text-transform: uppercase
}
app-circle-button {
  margin-right: 13px;
}


.cdk-drag-preview {
  box-sizing: border-box;
  border-radius: 4px;
  box-shadow: 0 5px 5px -3px rgba(0, 0, 0, 0.2),
              0 8px 10px 1px rgba(0, 0, 0, 0.14),
              0 3px 14px 2px rgba(0, 0, 0, 0.12);
}

.cdk-drag-placeholder {
  opacity: 0;
}

.cdk-drag-animating {
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}

.example-box:last-child {
  border: none;
}

.example-list.cdk-drop-list-dragging .example-box:not(.cdk-drag-placeholder) {
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}
.notifications-content-container {
  width: 480px;
  padding: 5px;
  .notification-option-container {
    .notification-option {
      margin: 7px;
      padding: 7px;
      text-align: left;
      border: 1px solid $color-light-grey;
      border-radius: 3px;
      * {
        font-size: 14px;
      }
      .notification-text {
        font-weight: 400;
        span {
          color: $color-secondary;
          //font-weight: ;
          font-weight: 700;
        }
        white-space: normal;
        line-height: 20px;
        margin-left: 5px;
      }
    }
    .active-notification {
      background-color: lighten($color-light-grey, 7);
    }
    .notification-option:hover {
      background-color: lighten($color-light-grey, 4);
    }
  }
}

.content-container {
  height: 36px;
  background-color: lighten($color-light, 5);
  transition: all 0.2s;

  > div {
    padding-top: 5px;
    height: 35px;

    img {
      margin-right: 5px;
      width: 23px;
      height: 23px;
    }
  }


}


.menu-item{
  cursor: pointer;
  z-index: 1;
  height: 50px;
  display: inline-block;
  position: relative;
  .drop-down-menu{
    display: none;
    position: absolute;
    background: white;
    border: 1px solid $color-light-grey;
    top: 60px;
  }
  .drop-down-menu[dir="ltr"] {
    right: 0;
  }
  .drop-down-menu[dir="rtl"] {
    left: 0;
  }


  .drop-down-menu:before {
    content: "";
    position: absolute;
    border: 11px solid transparent;
    border-bottom-color: white;
    top: -21px;
    z-index: 1;
  }

  .drop-down-menu:after {
    content: "";
    position: absolute;
    top: -21px;
    width: 0;
    height: 0;
    border: 10px solid transparent;
    border-bottom-color: $color-light-grey;
    z-index: 0;
  }
  .drop-down-menu[dir="ltr"]:before {
    right: 20px;
    margin-right: -10px;
  }
  .drop-down-menu[dir="rtl"]:before {
    left: 20px;
    margin-left: -10px;
  }

  .drop-down-menu[dir="ltr"]:after {
    right: 11px;
  }
  .drop-down-menu[dir="rtl"]:after {
    left: 11px;
  }
}

.show .drop-down-menu{
display: block;
}
.hidden {
  display: none;
}
