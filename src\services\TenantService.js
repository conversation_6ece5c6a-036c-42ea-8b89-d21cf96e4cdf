import Service from './Service';
import APIError from '../errors/APIError';
import Hospital from "../models/Hospital";
import Profile from "../models/Profile";
import Subscription from "../models/TenantSubscription";
import mongoose from 'mongoose';
import UserService from "../services/UserService";
import User from "../models/User";
import Staff from "../models/Staff";
import Supply from "../models/Supply";
import Drug from "../models/Drug";
import Radiograph from "../models/Radiograph";
import Biologie from "../models/Biologie";
import { generateUniqueCode } from "../helpers/general";
import {restructureProfileObj} from "../helpers/profileRework";
import PaginationService from "./PaginationService";

const userService = new UserService(User);

class TenantService extends Service {
    constructor(model) {
        super(model);
        this.manageTenant = this.manageTenant.bind(this);
        this.editTenant = this.editTenant.bind(this);
        this.getTenants = this.getTenants.bind(this);
        this.deleteTenant = this.deleteTenant.bind(this);
        this.getProducts = this.getProducts.bind(this);
        this.createOrEditProduct = this.createOrEditProduct.bind(this);
        this.deleteProduct = this.deleteProduct.bind(this);
    }

    async manageTenant(tenant,profile,pack,hospital, user) {

        const tenantID = tenant._id;
        const profileID = profile._id;
        const hospitalID = hospital._id;

        if(profile.email && !profileID){
            const user = await User.findOne({email: profile.email});
            if(user) throw new APIError(404, 'Email already in use');
        }
        const profileObj = Object.assign({},profile);
        delete profileObj._id

        if(!hospital.schedules){
            hospital.schedules=[1,2,3,4,5,6,0].map(x=>{
                return {
                    startTime: '09:00',
                    endTime: '17:00',
                    startBreak: '13:00',
                    endBreak: '14:00',
                    day:x
                }
            });
        }

        if(!tenantID){
            tenant.code = generateUniqueCode();
        }

        hospital = await Hospital.findOneAndUpdate({_id : mongoose.Types.ObjectId(hospitalID)},hospital,{new:true,upsert: true, setDefaultsOnInsert:true});
        tenant = await this.model.findOneAndUpdate({_id : mongoose.Types.ObjectId(tenantID)},tenant,{new:true,upsert: true, setDefaultsOnInsert:true});
        profile = await Profile.findOneAndUpdate({_id : mongoose.Types.ObjectId(profileID)},profileObj,{new:true,upsert: true, setDefaultsOnInsert:true});
        let admin = await Staff.findOneAndUpdate({profile : mongoose.Types.ObjectId(profileID)},profileObj,{new:true,upsert: true, setDefaultsOnInsert:true});

        if(!tenantID){
            let subscription = new Subscription({startDate: new Date(), actif: true,tenant: tenant._id , packs: [pack._id]});
            subscription = await subscription.save();
            tenant.subscription = subscription._id;
            tenant = await tenant.save();
            hospital.tenant = tenant._id;
            hospital = await hospital.save();
        }else{
            const oldSubscription = tenant.subscription;
            const oldPack = oldSubscription.packs[0];
            if(!oldPack._id.equals(pack._id) ){
              oldSubscription.terminated = true;
              oldSubscription.actif = false;
              oldSubscription.endDate = new Date();
              await oldSubscription.save()

              let subscription = new Subscription({startDate: new Date(), actif: true,tenant: tenant._id , packs: [pack._id]});
                subscription = await subscription.save();
                tenant.subscription = subscription._id;
                tenant = await tenant.save();
            }
        }

        if(!hospitalID){
            
            let consultation = {
                hospital : hospital._id,
                name:'Consultation',
                sellingPrice:100,
                type:'SESSION',
                avgDuration:30,
            }
            consultation = new Supply(consultation);
            consultation = await consultation.save();
            
            let control = {
                hospital : hospital._id,
                name:'Control',
                sellingPrice:100,
                type:'SESSION',
                avgDuration:20,
            }
            
            control = new Supply(control);
            control = await control.save();

            hospital.sessions = [control._id, consultation._id];
            hospital = await hospital.save();

            profile.hospital = hospital._id;
            admin.hospital = hospital._id;
            tenant.hospital = hospital._id;
            profile = await profile.save();
            tenant = await tenant.save();
            admin = await admin.save();
        }

        if(!profileID){
            hospital.doctors = [...hospital.doctors, mongoose.Types.ObjectId(admin._id)];
            hospital = await hospital.save();
            profile.staff = admin._id;
            profile = await profile.save();
            admin.doctors = [admin._id];
            admin.profile = profile._id;
            admin = await admin.save();
            if(profile.email) await userService.initializeUser(profile , 4);
        }
        profile = await profile.populate("staff");
        profile._doc = restructureProfileObj(profile._doc,true);
        
        return {tenant, pack, profile, hospital}
    }

    async editTenant(tenant,user) {
        tenant = await this.model.findOneAndUpdate({_id : mongoose.Types.ObjectId(tenant._id)},tenant).populate('hospital' , "-tenant").exec();
        return tenant
    }

    async getTenants(filters,user) {
        let query = {};
        if(filters.searchText) query.code = { $regex: filters.searchText, $options: "i" };
        if(filters.tenantID) query._id = mongoose.Types.ObjectId(filters.tenantID)
        let tenants = await this.model.find(query).populate({
            path : 'hospital' ,
            select:  "-tenant",
        }).exec();
        tenants = tenants.map(t => {
            let tenantObj = Object.assign({} , t._doc);
            if(tenantObj.hospital){
                let admin;
                let hospitalObj = Object.assign({} , t.hospital._doc);
                let doctors = tenantObj.hospital.doctors.map(p => {
                    let doctorObj = restructureProfileObj(p._doc,false);
                    if(p.isAdmin && !admin) admin = doctorObj
                    return doctorObj;
                })
                tenantObj.profile = admin;
                hospitalObj.doctors = doctors;
                tenantObj.hospital = hospitalObj;
            }
            if(tenantObj.subscription && tenantObj.subscription.packs) tenantObj.pack = t.subscription.packs[0];
            return tenantObj;
        })
        return tenants;
    }

    async deleteTenant(tenantID,user) {
        let tenant = await this.model.softDelete({_id: mongoose.Types.ObjectId(tenantID)}).populate('hospital' , "-tenant").exec();
        return tenant;
    }

    async getProducts(filters , user){
        const managerHospitals = await Hospital.find({isManager: true});
        const hospitalIds = [...managerHospitals.map(h => h._id) , mongoose.Types.ObjectId(user.profile.hospital._id)];
        let query= {hospital: {$in: hospitalIds}};

        if (filters.searchText) {
            if (!query["$and"]) query["$and"] = [];
            let or = [
                { name: { $regex: '^' +filters.searchText, $options: "i" } }
            ];
            if (filters.searchText.length == 12) or.push({ _id: mongoose.Types.ObjectId(filters.searchText) });
            query["$and"].push({ $or: or });
        }

        // If only one type is requested, use direct pagination
        if (filters.types.length === 1) {
            const type = filters.types[0];

            if (type === "DRUG") {
                // For drugs, we need to sort by drugFamily first, then by name
                // We'll use aggregation to achieve custom sorting
                const { page, limit } = PaginationService.validatePaginationParams(filters);
                const skip = (page - 1) * limit;

                const pipeline = [
                    { $match: query },
                    {
                        $lookup: {
                            from: 'drugfamilies',
                            localField: 'drugFamily',
                            foreignField: '_id',
                            as: 'drugFamily'
                        }
                    },
                    { $unwind: { path: '$drugFamily', preserveNullAndEmptyArrays: true } },
                    {
                        $addFields: {
                            familyOrder: {
                                $switch: {
                                    branches: [
                                        { case: { $eq: ['$drugFamily.name', 'Medicament'] }, then: 1 },
                                        { case: { $eq: ['$drugFamily.name', 'Complement Alimentaire'] }, then: 2 }
                                    ],
                                    default: 3
                                }
                            }
                        }
                    },
                    { $sort: { familyOrder: 1, name: 1 } },
                    {
                        $facet: {
                            docs: [
                                { $skip: skip },
                                { $limit: limit }
                            ],
                            totalCount: [
                                { $count: 'count' }
                            ]
                        }
                    }
                ];

                const result = await Drug.aggregate(pipeline);
                const docs = result[0].docs.map(d => {
                    d.type = "DRUG";
                    return d;
                });
                const total = (result[0].totalCount && result[0].totalCount[0] && result[0].totalCount[0].count) || 0;

                return PaginationService.createPaginationResponse(docs, total, page, limit);
            }
            if (type === "RADIOGRAPH") {
                let result = await Radiograph.paginate(query, options);
                let docs = result.docs.map(d => {
                    d._doc.type = "RADIOGRAPH";
                    return d;
                });
                // Use PaginationService to ensure consistent response format
                return PaginationService.createPaginationResponse(
                    docs,
                    result.total || result.totalDocs,
                    result.page,
                    result.limit
                );
            }
            if (type === "BIOLOGIE") {
                let result = await Biologie.paginate(query, options);
                let docs = result.docs.map(d => {
                    d._doc.type = "BIOLOGIE";
                    return d;
                });
                // Use PaginationService to ensure consistent response format
                return PaginationService.createPaginationResponse(
                    docs,
                    result.total || result.totalDocs,
                    result.page,
                    result.limit
                );
            }
        }

        // For multiple types, we still need to combine collections
        // But we'll optimize this by using aggregation pipeline
        let products = [];

        if(filters.types.includes("DRUG")){
            let drugs = await Drug.find(query).populate('drugFamily').sort({name: 1});
            drugs = drugs.map(d => {
                d._doc.type = "DRUG"
                return d
            })
            products = [...products , ...drugs]
        }
        if(filters.types.includes("RADIOGRAPH")){
            let radiographs = await Radiograph.find(query).sort({name: 1});
            radiographs = radiographs.map(d => {
                d._doc.type = "RADIOGRAPH"
                return d
            })
            products = [...products , ...radiographs]
        }
        if(filters.types.includes("BIOLOGIE")){
            let biologies = await Biologie.find(query).sort({name: 1});
            biologies = biologies.map(d => {
                d._doc.type = "BIOLOGIE"
                return d
            })
            products = [...products , ...biologies]
        }

        // Apply pagination manually for multiple types
        const { page, limit } = PaginationService.validatePaginationParams(filters);

        // Sort products by drugFamily first (Medicament, then Complement Alimentaire), then by name
        products.sort((a, b) => {
            // Helper function to get family order
            const getFamilyOrder = (product) => {
                if (product.drugFamily && product.drugFamily.name === 'Medicament') return 1;
                if (product.drugFamily && product.drugFamily.name === 'Complement Alimentaire') return 2;
                return 3; // Other families
            };

            const aFamilyOrder = getFamilyOrder(a);
            const bFamilyOrder = getFamilyOrder(b);

            // First sort by family order
            if (aFamilyOrder !== bFamilyOrder) {
                return aFamilyOrder - bFamilyOrder;
            }

            // Then sort by name
            return a.name.localeCompare(b.name);
        });

        return PaginationService.paginateArray(products, page, limit);
    }

    async createOrEditProduct(product , user) {
        if (!product.type) throw new APIError(404, 'Type missing');
        let productID = product._id;
        product.hospital=user.profile.hospital._id;
        product.updatedBy=user.profile._id;
        if(!productID) product.createdBy=user.profile._id;
        let query={_id : mongoose.Types.ObjectId(productID) , hospital:mongoose.Types.ObjectId(user.profile.hospital._id)};
        if(product.type === "DRUG") product = await Drug.findOneAndUpdate(query,product,{new:true,upsert: true, setDefaultsOnInsert:true});
        if(product.type === "RADIOGRAPH") product = await Radiograph.findOneAndUpdate(query,product,{new:true,upsert: true, setDefaultsOnInsert:true});
        if(product.type === "BIOLOGIE") product = await Biologie.findOneAndUpdate(query,product,{new:true,upsert: true, setDefaultsOnInsert:true});
        if (!product) throw new APIError(404, 'cannot create Product');
        return product
    }

    async deleteProduct(filters,user) {
        let product = {};
        if(!filters.productID) throw new APIError(404, 'ProductID missing');
        if(!filters.type) throw new APIError(404, 'Type missing');
        if(filters.type === "DRUG") product = await Drug.deleteOne({ hospital: mongoose.Types.ObjectId(user.profile.hospital._id) , _id: mongoose.Types.ObjectId(filters.productID)});
        if(filters.type === "RADIOGRAPH") product = await Radiograph.deleteOne({ hospital: mongoose.Types.ObjectId(user.profile.hospital._id) , _id: mongoose.Types.ObjectId(filters.productID)});
        if(filters.type === "BIOLOGIE") product = await Biologie.deleteOne({ hospital: mongoose.Types.ObjectId(user.profile.hospital._id) , _id: mongoose.Types.ObjectId(filters.productID)});
        return product
    }
}

export default TenantService;