import { Component, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { Session } from '../../../shared/models/session.model';
import { Invoice } from '../../../shared/models/invoice.model';
import { Appointment } from '../../../shared/models/appointment.model';
import { StorageService } from '../../../core/services/storage.service';
import { TranslateService } from '@ngx-translate/core';
import { SessionService } from '../../../shared/services/session.service';
import { SocketService } from '../../../core/services/socket.service';
import { Router } from '@angular/router';

interface DetailSession {
  session: Session;
  invoice: Invoice;
  futureAppointment: Appointment;
  historySessions: any;
}
@Component({
  selector: 'app-doctor',
  templateUrl: './doctor.component.html',
  styleUrls: ['./doctor.component.scss'],
})
export class DoctorComponent implements OnInit, On<PERSON><PERSON>roy {
  public isLoading: boolean = false;
  public detailSessions!: DetailSession[];

  public isArabicLanguageActive: boolean;
  public selectedSession: string;
  constructor(
    private storageService: StorageService,
    private translate: TranslateService,
    private sessionService: SessionService,
    private socketService: SocketService,
    private router: Router
  ) {
    this.isArabicLanguageActive = storageService.getCurrentLanguage() === 'ar';
    translate.onLangChange.subscribe(() => {
      this.isArabicLanguageActive = translate.currentLang === 'ar';
    });
  }
  ngOnInit(): void {
    this.isLoading = true;
    this.getCurrentSession();
  }

  getCurrentSession() {
    this.sessionService
      .getCurrentSession()
      .subscribe((res: DetailSession[]) => {
        this.isLoading = false;
        this.detailSessions = res;
        this.selectedSession = res[0]?.session?._id as string;
        this.detailSessions.forEach((detailSession) => {
          if (!detailSession.futureAppointment) {
            detailSession.futureAppointment = {
              date: detailSession.session.date,
              patient: detailSession.session.patient,
              doctor: detailSession.session.doctor,
            };
          }
          detailSession.historySessions = detailSession.historySessions?.docs;
        });

        this.socketListnerForAppointmentsChange();
        // this.flattenPrescriptions();
      }, err => {
        this.isLoading = false;
      });
  }

  socketListnerForAppointmentsChange() {
    this.detailSessions?.forEach((detailSession) => {
      if (detailSession?.session?._id) {
        this.socketService
          .listen(detailSession.session?._id + '')
          .subscribe((res: any) => {
            if (res.session) {
              // detailSession.session = res.session;
            }
          });
      }
    });
  }

  handleSessionFinish(session: Session) {
    if (this.detailSessions.length > 1) {
      this.removeListeners();
      this.getCurrentSession();
    } else {
      this.router.navigate(['/doctor/summary']);
    }
  }

  removeListeners() {
    this.detailSessions?.forEach((detailSession) => {
      if (detailSession.session?._id) {
        this.socketService.removeAllListeners(detailSession.session._id + '');
      }
    });
  }
  ngOnDestroy(): void {
    this.removeListeners();
  }
}
