<div
  class="appointments-page-container"
  infiniteScroll
  [infiniteScrollDistance]="2"
  [infiniteScrollThrottle]="50"
  (scrolled)="onScroll()"
  [scrollWindow]="false"
  fxLayout="column"
>
  <div class="container-fluid content-section">
    <div
      fxLayout="row"
      fxLayoutAlign="space-evenly center"
      class="options-bar-container"
    >
      <div class="d-flex justify-content-center w-50 first-line-option">
        <mat-button-toggle-group name="fontStyle" aria-label="Font Style">
          <mat-button-toggle
            fxFlex
            *ngFor="let viewType of ['DAILY', 'WEEKLY', 'MONTHLY']"
            (click)="onViewTypeChange($any(viewType))"
            [checked]="viewType === selectedViewType"
            [value]="viewType"
          >
            {{ frenshVal(viewType) | translate }}
          </mat-button-toggle>
        </mat-button-toggle-group>
      </div>

      <div fxFlex class="date-picker-container w-50 first-line-option">
        <app-date-picker *ngIf="selectedViewType === 'DAILY'"
          [dir]="isArabicLanguageActive ? 'rtl' : 'ltr'"
          (dateSwitch)="onDayChange($event)"
          (SelectedDay)="chosenDayHandler($event)"
          [day]="date"
        ></app-date-picker>
        <app-costum-date-picker-week *ngIf="selectedViewType === 'WEEKLY'"
                         [dir]="isArabicLanguageActive ? 'rtl' : 'ltr'"
                         (dateSwitch)="onDayChange($event)"
                                     (weekPicked)="onWeekPick($event)"
                         [day]="date"
        ></app-costum-date-picker-week>
        <app-custom-date-picker-small *ngIf="selectedViewType === 'MONTHLY'"
                                     [dir]="isArabicLanguageActive ? 'rtl' : 'ltr'"
                                     (dateSwitch)="onDayChange($event)"
                                      (monthPicked)="onMonthPick($event)"
                                     [day]="date"
        ></app-custom-date-picker-small>
      </div>

    </div>
    <div
      fxLayout="row"
      fxLayoutAlign="space-evenly center"
      class="options-bar-container"
    >
      <div
        fxFlex
        fxLayout="row"
        fxLayoutAlign="space-between center"
        fxLayoutGap="10px"
      >
        <mat-form-field appearance="legacy">
          <mat-label>{{ 'appointments.search' | translate }}</mat-label>
          <input
            matInput
            class="appointments-search-input"
            [placeholder]="'general.searchPlaceHolder' | translate"
            (input)="searchAppointments($event)"

            [value]="searchText"
          />
          <mat-icon matSuffix>search</mat-icon>
        </mat-form-field>
        <app-select-doctor
          [doctors]="allowedDoctors"
          [selectedDoctors]="doctors"
          (doctorIDsChangeEvent)="selectDoctorChange($event)"
        ></app-select-doctor>
      </div>
      <div fxFlex
           fxLayout="row"
           fxLayoutAlign="space-between center"
           fxLayoutGap="10px" class="d-flex justify-content-center">
<!--        Stats-->
      </div>
      <div
        fxLayoutGap="10px"
        fxFlex
        class="click-options"
        fxLayout="row"
        fxLayoutAlign="end center"
      >
        <app-text-check-toggle
          (change)="toggleTreatedChange($event)"
          [checked]="treated"
          >{{ 'appointments.passedSessions' | translate }}
        </app-text-check-toggle>
        <div
          class="menu-item"
          fxLayout="column"
          #timeoffsOptionsRef
          [matTooltip]="'appointments.tooltips.breakList' | translate"
          (click)="this.showTimeoffs = !this.showTimeoffs"
          [ngClass]="{ show: showTimeoffs }"
          fxLayoutAlign="start center"
        >
          <div class="icons-container">
            <img class="item-icon" src="assets/icons/timeoff.svg" />
          </div>
          <div
            [dir]="isArabicLanguageActive ? 'rtl' : 'ltr'"
            class="drop-down-menu dynamic-shadow notifications-content-container"
          >
            <div *ngIf="timeoffs.length > 0; else noTimeoffs">
              <app-small-appointment
                [dir]="isArabicLanguageActive ? 'rtl' : 'ltr'"
                *ngFor="let timeoff of timeoffs; let i = index"
                class="notification-option notification-option-container"
                fxLayout="column"
                fxLayoutGap="05px"
                fxLayoutAlign="start center"
                [appointment]="timeoff"
              ></app-small-appointment>
            </div>
            <ng-template #noTimeoffs>
              <app-no-results
                notification-option
                notification-option-container
                [isSmall]="true"
                [showImage]="false"
                >{{ 'appointments.noBreaksFound' | translate }}
              </app-no-results>
            </ng-template>
          </div>
        </div>

        <app-circle-button
          name="add"
          [matTooltip]="'appointments.tooltips.addAppointment' | translate"
          (click)="createClick()"
        ></app-circle-button>
      </div>
    </div>
    <div [ngClass]="{'hidden': selectedViewType !== 'WEEKLY'}">
      <app-appointments-calendar type="WEEKLY" [allowedDoctors]="allowedDoctors" [selectedDoctors]="doctors" [inProgressAppointments]="inProgressAppointments" (appointmentClick)="updateClick($event)" [appointments]="appointments" [date]="date"></app-appointments-calendar>
    </div>
    <div [ngClass]="{'hidden': selectedViewType !== 'MONTHLY'}">
      <app-appointments-calendar (dayClickEvent)="handleMonthDayClick($event)" type="MONTHLY" [allowedDoctors]="allowedDoctors" [selectedDoctors]="doctors" [inProgressAppointments]="inProgressAppointments" (appointmentClick)="updateClick($event)" [appointments]="appointments" [date]="date"></app-appointments-calendar>
    </div>
    <div cdkDropListGroup *ngIf="selectedViewType === 'DAILY'">
      <div
        cdkDropList
        #waiting="cdkDropList"
        [cdkDropListData]="inProgressAppointments"
        [cdkDropListConnectedTo]="[inProgress]"
        (cdkDropListDropped)="drop($event)"
      >
        <div *ngFor="let appointment of inProgressAppointments; let i = index">
          <app-appointment
            [dir]="isArabicLanguageActive ? 'rtl' : 'ltr'"
            [id]="appointment._id"
            [isFirstAppointment]="i === 0"
            [appointment]="appointment"
            [showUpdatedBackground]="isJustUpdated(appointment)"
            (appointmentUpdatedEvent)="manageUpdate($event, true, false)"
            *ngIf="isAppointment(appointment); else timeoff"
          ></app-appointment>
          <ng-template #timeoff>
            <app-timeoff
              [dir]="isArabicLanguageActive ? 'rtl' : 'ltr'"
              cdkDrag
              [cdkDragDisabled]="appointment.state !== 'APPROVED'"
              [timeoff]="appointment"
              [id]="appointment._id"
              [showUpdatedBackground]="isJustUpdated(appointment)"
              (timeoffUpdatedEvent)="manageUpdateTimeoff($event)"
            ></app-timeoff>
          </ng-template>
        </div>

        <app-no-results
          [isSmall]="true"
          [showImage]="false"
          *ngIf="
            !isLoadingAppointments &&
            inProgressAppointments.length === 0 &&
            !treated &&
            appointments.length > 0
          "
        >
          {{ 'appointments.noAppointmentsInHold' | translate }}
        </app-no-results>
      </div>

      <div class="mt-4">
        <app-seperator
          *ngIf="
            appointments &&
            appointments.length > 0 &&
            inProgressAppointments &&
            inProgressAppointments.length > 0
          "
        >
        </app-seperator>
      </div>
      <div
        cdkDropList
        #inProgress="cdkDropList"
        [cdkDropListData]="appointments"
        [cdkDropListConnectedTo]="[waiting]"
        (cdkDropListDropped)="drop($event)"
      >
        <div *ngFor="let appointment of appointments; let i = index">
          <app-appointment
            cdkDrag
            [dir]="isArabicLanguageActive ? 'rtl' : 'ltr'"
            [id]="appointment._id"
            [appointment]="appointment"
            [showUpdatedBackground]="isJustUpdated(appointment)"
            (appointmentUpdatedEvent)="manageUpdate($event, false, false)"
            [cdkDragDisabled]="appointment.state !== 'APPROVED'"
            *ngIf="isAppointment(appointment); else timeoff"
          ></app-appointment>
          <ng-template #timeoff>
            <app-timeoff
              cdkDrag
              [cdkDragDisabled]="appointment.state !== 'APPROVED'"
              [timeoff]="appointment"
              [id]="appointment._id"
              [showUpdatedBackground]="isJustUpdated(appointment)"
              (timeoffUpdatedEvent)="manageUpdateTimeoff($event)"
            ></app-timeoff>
          </ng-template>
        </div>

        <app-no-results
          *ngIf="
            !isLoadingAppointments &&
            appointments.length === 0 &&
            inProgressAppointments.length === 0
          "
        >
          {{ 'appointments.noAppointmentsFound' | translate }}
        </app-no-results>
      </div>
    </div>

    <div *ngIf="(page <= pages || isLoadingAppointments) && selectedViewType === 'DAILY'">
      <app-long-card></app-long-card>
      <app-long-card></app-long-card>
    </div>
  </div>
</div>
