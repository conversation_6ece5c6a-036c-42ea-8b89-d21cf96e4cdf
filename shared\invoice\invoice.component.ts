import { Component, Inject, Input, OnInit } from '@angular/core';
import { StorageService } from '../../../core/services/storage.service';
import { User } from '../../models/user.model';
import { Session } from '../../models/session.model';
import { Supply } from '../../models/supply.model';
import { SupplyService } from '../../services/supply.service';
import { Subscription } from 'rxjs';
import { MatAutocompleteSelectedEvent } from '@angular/material/autocomplete';
import { Invoice } from '../../models/invoice.model';
import { InvoiceItem } from '../../models/invoice-item.model';
import { SessionService } from '../../services/session.service';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { exportAsPdf } from '../../utils/pdf.functions';
import { TranslateService } from '@ngx-translate/core';
import { Direction } from '@angular/cdk/bidi';
import { APPOINTMENT_STATES_OBJECT } from '../../constants/defaults.consts';


export function compareObjetFn(a: any, b: any) {
  if (a && b) {
    return a._id === b.supply?._id;
  }
  else if (a && !b) {
    return false;
  }
  else if (!a && b) {
    return false;
  }
  else {
    return true;
  }
}
@Component({
  selector: 'app-invoice',
  templateUrl: './invoice.component.html',
  styleUrls: ['./invoice.component.scss'],
})
export class InvoiceComponent implements OnInit {
  @Input() session: Session;
  public suppliesSuggestions: any[] = [];
  public currentUser: User;
  public isLoadingPDF: boolean;
  public isInvoiceLoading: boolean;
  public isCUInvoiceLoading: boolean;
  public invoice: Invoice = {
    items: [],
  };
  compareFn = compareObjetFn.bind(this);
  APPOINTMENT_STATES_OBJECT = APPOINTMENT_STATES_OBJECT;


  private emptyInvoice: InvoiceItem = {
    name: '',
    description: '',
    quantity: 1,
    price: 0,
  };
  public dir: Direction = 'ltr';
  public isArabic: boolean;

  constructor(
    private storageService: StorageService,
    private supplyService: SupplyService,
    private sessionService: SessionService,
    public dialogRef: MatDialogRef<InvoiceComponent>,
    @Inject(MAT_DIALOG_DATA)
    public data: { invoice: Invoice; session: Session, suppliesSuggestions: Supply[] },
    private translate: TranslateService
  ) {
    this.currentUser = this.storageService.getUser();
    this.dir = translate.currentLang === 'ar' ? 'rtl' : 'ltr';
    this.isArabic = translate.currentLang === 'ar';
    translate.onLangChange.subscribe(() => {
      this.dir = translate.currentLang === 'ar' ? 'rtl' : 'ltr';
      this.isArabic = translate.currentLang === 'ar';
    });
  }

  ngOnInit(): void {
    if (this.data.invoice) {
      // Create a deep copy to avoid reference issues
      this.invoice = JSON.parse(JSON.stringify(this.data.invoice));
    }
    if (this.data.suppliesSuggestions) {
      this.suppliesSuggestions = this.data.suppliesSuggestions;
    }
    // this.getSupplies('');
  }

  getWidth(sellingPrice?: any) {
    const length = (sellingPrice as number).toString().length;
    return length > 3 ? length : 3;
  }

  getItemTotal(sellingPrice?: any, quantity?: any) {
    return ((sellingPrice as number) * (quantity as number)).toFixed(2);
  }

  getTotal() {
    const total = this.invoice.items
      ?.map((item: any) =>
        this.getItemTotal(item.price as number, item.quantity as number)
      )
      .reduce((total: number, num: string) => {
        return total + parseFloat(num);
      }, 0)
      .toFixed(2);

    return total;
  }

  addItem() {
    this.invoice.items?.push(JSON.parse(JSON.stringify(this.emptyInvoice)));
  }



  inputChange(item: any, $event: any) {
    item.name = $event.target.value;
    // this.getSupplies($event.target?.value);
  }

  setItem($event: any, i: number) {
    const item = $event?.value ? $event?.value : {name: '', description: '', sellingPrice: 0};
    if (this.invoice?.items && this.invoice?.items[i]) {
      this.invoice.items[i].name = (item as Supply).name;
      this.invoice.items[i].description = (item as Supply).description;
      this.invoice.items[i].price = (item as Supply).sellingPrice;
      this.invoice.items[i].supply = item as Supply;
    }
  }

  onPriceChange(value: any, itemIndex: number) {
    if (this.invoice.items && this.invoice.items[itemIndex]) {
      // Ensure the value is a number
      const numericValue = parseFloat(value);
      this.invoice.items[itemIndex].price = isNaN(numericValue) ? 0 : numericValue;

      // Update the total immediately
      this.invoice.total = this.calculateTotal();
    }
  }

  onQuantityChange(value: any, itemIndex: number) {
    if (this.invoice.items && this.invoice.items[itemIndex]) {
      // Ensure the value is a number
      const numericValue = parseInt(value, 10);
      this.invoice.items[itemIndex].quantity = isNaN(numericValue) ? 0 : numericValue;

      // Update the total immediately
      this.invoice.total = this.calculateTotal();
    }
  }

  // Keep the old methods for backward compatibility if needed elsewhere
  updatePrice($event: any, itemIndex: number) {
    $event.preventDefault();
    if (this.invoice.items) {
      if (!isNaN($event.target.value) && $event.target.value.length > 0) {
        this.invoice.items[itemIndex].price = parseFloat($event.target.value);
      } else {
        this.invoice.items[itemIndex].price = 0;
      }
    }
  }

  updateQuantity($event: any, itemIndex: number) {
    $event.preventDefault();
    if (this.invoice.items) {
      if (!isNaN($event.target.value) && $event.target.value.length > 0) {
        this.invoice.items[itemIndex].quantity = parseInt($event.target.value, 10);
      } else {
        this.invoice.items[itemIndex].quantity = 0;
      }
    }
  }

  removeItem(itemIndex: number) {
    this.invoice.items?.splice(itemIndex, 1);
  }

  exportAsPDF(print: boolean = false) {
    this.isLoadingPDF = true;
    exportAsPdf(
      'invoice-container' + this.invoice?.session?.appointment?._id,
      print,
      this.storageService.getCurrentLanguage()
    ).subscribe(() => {
      this.isLoadingPDF = false;
    });
  }

  createUpdateInvoice() {
    // Ensure all numeric values are properly converted
    this.sanitizeInvoiceData();

    this.sessionService.createUpdateInvoice(this.invoice).subscribe(() => {
      this.dialogRef.close(true);
    });
  }

  private sanitizeInvoiceData() {
    if (this.invoice.items) {
      this.invoice.items.forEach((item: any) => {
        // Ensure price and quantity are numbers
        item.price = typeof item.price === 'string' ? parseFloat(item.price) || 0 : item.price || 0;
        item.quantity = typeof item.quantity === 'string' ? parseInt(item.quantity, 10) || 0 : item.quantity || 0;

        // Ensure tax is a number (if it exists)
        if (item.tax !== undefined) {
          item.tax = typeof item.tax === 'string' ? parseFloat(item.tax) || 0 : item.tax || 0;
        }
      });

      // Calculate and update the total
      this.invoice.total = this.calculateTotal();
    }
  }

  private calculateTotal(): number {
    if (!this.invoice.items || this.invoice.items.length === 0) {
      return 0;
    }

    return this.invoice.items.reduce((total: number, item: any) => {
      const itemTotal = (item.price || 0) * (item.quantity || 0);
      return total + itemTotal;
    }, 0);
  }

  closeInvoice($event: MouseEvent) {
    $event.preventDefault();
    this.dialogRef.close();
  }
}
