// Modern Professional Invoice Styles
$width: 148mm;
$height: 210mm;

// Color Palette - Black & White Theme
$primary-color: #000000;
$secondary-color: #333333;
$accent-color: #666666;
$text-dark: #000000;
$text-light: #666666;
$border-light: #cccccc;
$background-light: #f8f9fa;
$white: #ffffff;

.pdf-container {
  height: 0;
  overflow-y: hidden;
  direction: ltr;
}

.invoice-container {
  width: $width;
  height: $height;
  padding: 8mm;
  color: $text-dark;
  background: $white;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  font-size: 10px;
  line-height: 1.4;
  box-sizing: border-box;
}

.invoice-content {
  height: 100%;
  position: relative;
  display: flex;
  flex-direction: column;

  // Modern Header Styles
  .modern-header {
    margin-bottom: 20px;

    .header-top {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 15px;
      padding-bottom: 10px;
      border-bottom: 2px solid $secondary-color;

      .invoice-title {
        h1 {
          font-size: 1.8em;
          font-weight: 300;
          color: $primary-color;
          margin: 0 0 6px 0;
          letter-spacing: -0.5px;
        }

        .invoice-number {
          display: flex;
          align-items: center;
          gap: 6px;

          .invoice-label {
            font-size: 0.8em;
            color: $text-light;
            font-weight: 500;
          }

          .invoice-id {
            background: $secondary-color;
            color: $white;
            padding: 4px 8px;
            border-radius: 3px;
            font-weight: 600;
            font-size: 0.8em;
            letter-spacing: 0.5px;
          }
        }
      }

      .invoice-date {
        text-align: right;

        .date-label {
          font-size: 0.75em;
          color: $text-light;
          margin-bottom: 3px;
          text-transform: uppercase;
          letter-spacing: 0.3px;
        }

        .date-value {
          font-size: 0.9em;
          font-weight: 600;
          color: $primary-color;
        }
      }
    }

    .header-info {
      display: flex;
      justify-content: space-between;
      gap: 20px;

      .company-info {
        flex: 1;

        h3 {
          font-size: 1.1em;
          font-weight: 600;
          color: $primary-color;
          margin: 0 0 8px 0;
          border-left: 3px solid $secondary-color;
          padding-left: 8px;
        }

        .company-details {
          .info-line {
            display: flex;
            align-items: center;
            margin-bottom: 4px;
            color: $text-dark;
            font-size: 0.9em;

            .icon-location::before,
            .icon-phone::before,
            .icon-email::before {
              content: "📍";
              margin-right: 6px;
              font-size: 0.8em;
            }

            .icon-phone::before {
              content: "📞";
            }

            .icon-email::before {
              content: "✉️";
            }
          }
        }
      }

      .patient-info {
        flex: 1;
        background: $background-light;
        padding: 12px;
        border-radius: 6px;
        border-left: 3px solid $accent-color;

        h4 {
          font-size: 0.9em;
          font-weight: 600;
          color: $primary-color;
          margin: 0 0 8px 0;
          text-transform: uppercase;
          letter-spacing: 0.3px;
        }

        .patient-details {
          .patient-name {
            font-size: 0.95em;
            font-weight: 600;
            color: $text-dark;
            margin-bottom: 4px;
          }

          .patient-email {
            color: $text-light;
            font-size: 0.85em;
          }
        }
      }
    }
  }

  // Minimal Header Styles (when prescriptionHeader is disabled)
  .minimal-header {
    margin-bottom: 20px;

    .minimal-spacing {
      height: 50px; // Add space at top when no header
    }

    .patient-section {
      .patient-details {
        .invoice-number-minimal,
        .patient-info,
        .date-info {
          margin: 8px 0;
          font-size: 0.9em;
          color: $text-dark;

          .label {
            font-weight: 600;
            color: $text-light;
            margin-right: 8px;
          }
        }

        .invoice-number-minimal {
          font-weight: 700;
          color: $primary-color;
          font-size: 1em;
          margin-bottom: 12px;
        }
      }
    }
  }

  // Minimal Footer Styles
  .minimal-footer {
    margin-top: auto;
    height: 20px; // Just some space
  }

  // Modern Main Content
  .invoice-main {
    flex: 1;
    margin-bottom: 20px;

    .items-section {
      .section-title {
        font-size: 1.1em;
        font-weight: 600;
        color: $primary-color;
        margin: 0 0 12px 0;
        padding-bottom: 6px;
        border-bottom: 1px solid $border-light;
      }

      .items-table {
        background: $white;
        border-radius: 6px;
        overflow: hidden;
        box-shadow: 0 1px 6px rgba(0, 0, 0, 0.1);
        margin-bottom: 15px;

        .table-header {
          display: flex;
          background: $primary-color;
          color: $white;
          font-weight: 600;
          font-size: 0.8em;
          text-transform: uppercase;
          letter-spacing: 0.3px;

          > div {
            padding: 10px 8px;
            border-right: 1px solid rgba(255, 255, 255, 0.1);

            &:last-child {
              border-right: none;
            }
          }

          .col-description {
            flex: 2;
          }

          .col-price,
          .col-quantity,
          .col-total {
            flex: 1;
            text-align: center;
          }
        }

        .table-body {
          .table-row {
            display: flex;
            border-bottom: 1px solid $border-light;

            &:last-child {
              border-bottom: none;
            }

            > div {
              padding: 8px 6px;
              border-right: 1px solid $border-light;

              &:last-child {
                border-right: none;
              }
            }

            .col-description {
              flex: 2;

              .item-name {
                font-weight: 600;
                color: $text-dark;
                margin-bottom: 2px;
                font-size: 0.9em;
              }

              .item-description {
                font-size: 0.75em;
                color: $text-light;
                font-style: italic;
              }
            }

            .col-price,
            .col-quantity,
            .col-total {
              flex: 1;
              text-align: center;
              display: flex;
              align-items: center;
              justify-content: center;
              font-size: 0.85em;

              .amount {
                font-weight: 600;
                color: $text-dark;
              }

              .currency {
                font-size: 0.75em;
                color: $text-light;
                margin-left: 3px;
                font-weight: 500;
              }

              .qty-value {
                background: $background-light;
                padding: 2px 6px;
                border-radius: 3px;
                font-weight: 600;
                color: $primary-color;
                font-size: 0.8em;
              }
            }
          }
        }
      }

      .invoice-summary {
        background: $background-light;
        padding: 12px;
        border-radius: 6px;
        border-left: 3px solid $secondary-color;

        .summary-row {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 6px;

          &:last-child {
            margin-bottom: 0;
          }

          &.subtotal {
            padding-bottom: 6px;
            border-bottom: 1px solid $border-light;
            font-size: 0.85em;

            .summary-label {
              color: $text-light;
            }

            .summary-value {
              color: $text-dark;
              font-weight: 500;
            }
          }

          &.total {
            font-size: 1em;
            font-weight: 700;
            color: $primary-color;
            padding-top: 6px;

            .summary-label {
              text-transform: uppercase;
              letter-spacing: 0.3px;
            }

            .summary-value {
              background: $secondary-color;
              color: $white;
              padding: 6px 12px;
              border-radius: 4px;
              font-size: 0.9em;
            }
          }
        }
      }
    }
  }

  // Modern Footer
  .modern-footer {
    margin-top: auto;
    padding-top: 12px;
    border-top: 1px solid $border-light;

    .footer-content {
      display: flex;
      justify-content: space-between;
      align-items: flex-end;

      .footer-left {
        .thank-you {
          font-size: 0.9em;
          font-weight: 600;
          color: $primary-color;
          margin-bottom: 4px;
        }

        .footer-note {
          font-size: 0.75em;
          color: $text-light;
        }
      }

      .footer-right {
        text-align: right;

        .invoice-info {
          .generated-date {
            font-size: 0.7em;
            color: $text-light;
            font-style: italic;
          }
        }
      }
    }
  }
}

// Print-specific styles
@media print {
  .invoice-container {
    box-shadow: none;
    border: none;
    margin: 0;
    padding: 15mm;
  }

  .modern-header .header-info {
    page-break-inside: avoid;
  }

  .items-table {
    page-break-inside: avoid;
  }

  .invoice-summary {
    page-break-inside: avoid;
  }
}

// Utility classes for better UX
.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.text-left {
  text-align: left;
}

.font-weight-bold {
  font-weight: 600;
}

.font-weight-light {
  font-weight: 300;
}

.mb-0 {
  margin-bottom: 0;
}

.mb-1 {
  margin-bottom: 8px;
}

.mb-2 {
  margin-bottom: 16px;
}

.mb-3 {
  margin-bottom: 24px;
}

// Animation for better UX (subtle)
.table-row {
  transition: all 0.2s ease;
}

.invoice-number .invoice-id {
  transition: all 0.3s ease;
}

// Accessibility improvements
.invoice-content {
  * {
    &:focus {
      outline: 2px solid $secondary-color;
      outline-offset: 2px;
    }
  }
}

// High contrast mode support
@media (prefers-contrast: high) {
  .invoice-container {
    color: #000000;
    background: #ffffff;
  }

  .modern-header .header-top {
    border-bottom-color: #000000;
  }

  .items-table .table-header {
    background: #000000;
    color: #ffffff;
  }
}
