@import '../../../../theming/variables';

.rend-as .stat-rendez {
  text-align: right
}

.appointment-left-time {
  width: 30px;
}
.rend-as {
  background-color: #fff;
  border: 1px solid #dde1e2;
  border-radius: 5px;
  cursor: pointer;
  align-items: center;
  padding: .5rem 0;
  transition: .5s ease-in;
  -webkit-transition: .5s ease-in
}

.rend-as:active, .rend-as:hover {
  box-shadow: 0 0 3px 0 $color-primary;
  border: 1px solid $color-primary;
  transition: .2s ease-in;
  -webkit-transition: .2s ease-in
}

.rend-as small {
  color: #263448
}


.rend-as .titre-time {
  color: #263448;
  font-weight: 500;
  margin: 0;
  padding: 0;
  white-space: nowrap;
  font-size: 14px;
}

.rend-as .time-h {
  text-transform: none;
  margin: 0;
  padding: 0
}

@media screen and (max-width: 767px) {
  .rend-as .time-h {
    margin-bottom: 2rem
  }
}


@media screen and (max-width: 767px) {
  .rend-as .stat-rendez {
    text-align: center;
    margin-bottom: 2rem
  }
}


.dropdown-items {
  position: absolute;
  will-change: transform;
  transform: translate3d(-50px, 32px, 0px);
  padding: .5rem;
  top: 0;
  right: 0;
  background-color: white;

}

.ticket-container {
  margin-left: 10px;

  .ticket-content {
    border: 1px solid #00000020;
    border-radius: 5px;
    padding-right: 10px;
    padding-left: 10px;

    small {
      font-size: 1em;
      font-weight: 600;
    }

    .n-ticket {
      color: #88949e;
      margin: 0;
      padding: 0;
      font-weight: 600
    }
  }
}

app-circle-button {
  margin-right: 12px;
}

.dropdown-item {
  font-size: .8rem;
  border-radius: 5px;
  margin-bottom: 7px;

  span {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: .6rem;
    display: inline-block
  }

  mat-icon {
    width: 18px;
    height: 18px;
    font-size: 18px;
    margin-right: .6rem;
  }
}
.dropdown-menu[dir="rtl"] {
  .dropdown-item{
    span {
      margin-right: 0;
      margin-left: .6rem;
    }
    mat-icon {
      margin-right: 0;
      margin-left: .6rem;
    }
  }

}

.dropdown-item:last-child {
  margin-bottom: 0;
}

.dropdown-menu {
  padding: .5rem
}

.stat-rendez {
  img {
    width: 20px;
    height: 20px;
  }
}

.disabled-button {
  color: $lighter-gray !important;
}

.hidden {
  visibility: hidden;
}
