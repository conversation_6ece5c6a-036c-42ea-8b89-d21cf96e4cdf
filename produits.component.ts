import {Component, On<PERSON><PERSON>roy, OnInit} from '@angular/core';
import {Direction} from '@angular/cdk/bidi';
import {Subscription} from 'rxjs';
import {MatBottomSheet} from '@angular/material/bottom-sheet';
import {StorageService} from '../../../../core/services/storage.service';
import {TranslateService} from '@ngx-translate/core';
import {PrescriptionService} from '../../../../shared/services/prescription.service';
import {DrugDialogComponent} from '../../../../shared/components/drug-dialog/drug-dialog.component';
import {BiologieDialogComponent} from '../../../../shared/components/biologie-dialog/biologie-dialog.component';
import {RadiologieDialogComponent} from '../../../../shared/components/radiologie-dialog/radiologie-dialog.component';
import {CALLS_TYPES} from '../../../../shared/constants/defaults.consts';


export type PrescriptionType = 'ORDONNANCE' | 'RADIOLOGIE' | 'BIOLOGIE' | 'AUTRE';

@Component({
  selector: 'app-produits',
  templateUrl: './produits.component.html',
  styleUrls: ['./produits.component.scss']
})
export class ProduitsComponent implements OnInit, OnDestroy {

  public Math = Math; // For template usage

  public isLoadingProducts = false;
  public products: any[] = [];
  public page: number = 1;
  public limit: number = 10;
  public totalPages: number = 0;
  public totalItems: number = 0;
  public hasNextPage: boolean = false;
  public hasPrevPage: boolean = false;
  public searchText: string = '';
  public dir: Direction = 'ltr';
  public limitOptions: number[] = [10, 20, 30, 40, 50];

  prescriptionType: PrescriptionType = 'ORDONNANCE';


  private getProductsSubscription!: Subscription;

  constructor(
    private prescriptionsService: PrescriptionService,
    private dialog: MatBottomSheet,
    private storageService: StorageService,
    private translate: TranslateService
  ) {
    this.dir = translate.currentLang === 'ar' ? 'rtl' : 'ltr';
    translate.onLangChange.subscribe(() => {
      this.dir = translate.currentLang === 'ar' ? 'rtl' : 'ltr';
    });
  }

  ngOnInit(): void {
    this.getProducts();
  }

  getProducts() {
    this.isLoadingProducts = true;

    this.getProductsSubscription = this.prescriptionsService
      .getProducts(this.searchText, this.typeTransformer, this.page, this.limit)
      .subscribe((res: any) => {
        this.isLoadingProducts = false;

        // Always replace products (no more appending)
        this.products = res.docs || res;

        // Handle pagination metadata if available
        if (res.pages !== undefined) {
          this.totalPages = res.pages;
          this.totalItems = res.total;
          this.hasNextPage = res.hasNextPage;
          this.hasPrevPage = res.hasPrevPage;
        }
      }, (error) => {
        this.isLoadingProducts = false;
        console.error('Error loading products:', error);
      });
  }



  goToPage(page: number) {
    if (page >= 1 && page <= this.totalPages && page !== this.page) {
      this.page = page;
      this.getProducts();
    }
  }

  nextPage() {
    if (this.hasNextPage && !this.isLoadingProducts) {
      this.page = this.page + 1;
      this.getProducts();
    }
  }

  prevPage() {
    if (this.hasPrevPage && !this.isLoadingProducts) {
      this.page = this.page - 1;
      this.getProducts();
    }
  }

  onLimitChange(newLimit: number) {
    this.limit = newLimit;
    this.page = 1; // Reset to first page when changing limit
    this.getProducts();
  }

  ngOnDestroy(): void {
    if (this.getProductsSubscription) {
      this.getProductsSubscription.unsubscribe();
    }
  }



  resetData() {
    this.page = 1;
    this.products = [];
    this.totalPages = 0;
    this.totalItems = 0;
    this.hasNextPage = false;
    this.hasPrevPage = false;
  }



  searchProducts($event: any) {
    this.searchText = $event.target?.value;
    this.resetData();
    this.getProducts();
  }

  createClick() {
    let openComp: any = DrugDialogComponent;
    let name: string = 'drug';
    if (this.prescriptionType === 'ORDONNANCE') {
      openComp = DrugDialogComponent;
      name = 'drug';

    } else if (this.prescriptionType === 'BIOLOGIE') {
      openComp = BiologieDialogComponent;
      name = 'bio';
    }
    else if (this.prescriptionType === 'RADIOLOGIE') {
      openComp = RadiologieDialogComponent;
      name = 'radio';
    }
    const dialogRef = this.dialog.open(openComp, {
      data: {
        type: CALLS_TYPES.create,
        [name]: {},
      },
    });

    dialogRef.afterDismissed().subscribe((product) => {
      if (product && product._id) {
        this.resetData();
        this.getProducts();
      }
    });
  }

  manageDelete(product: any) {
    this.products = this.products.filter((x) => x._id !== product._id);
    this.getProducts();
  }

  manageUpdate(product: any) {
    this.setProduct(product);
    this.sortProducts();
  }

  setProduct(product: any) {
    const productIndex = this.products.findIndex(
      (app) => app._id === product._id
    );
    this.products[productIndex] = JSON.parse(JSON.stringify(product));
  }

  sortProducts() {
    this.products = JSON.parse(JSON.stringify(this.products));
  }

  onViewTypeChange(viewType: PrescriptionType) {
    if (this.prescriptionType !== viewType) {
      this.prescriptionType = viewType;
      this.resetData();
      this.getProducts();
    }
  }

  get typeTransformer() {
    const type = this.prescriptionType;
    if (type === 'ORDONNANCE') { return 'DRUG'; }
    if (type === 'RADIOLOGIE') { return 'RADIOGRAPH'; }
    if (type === 'BIOLOGIE') { return 'BIOLOGIE'; }
    return 'DRUG';
  }

}
