import {AfterViewInit, Component, EventEmitter, Input, OnChanges, OnInit, Output, Renderer2} from '@angular/core';
import dayGridPlugin from '@fullcalendar/daygrid';
import interactionPlugin from '@fullcalendar/interaction';
import timeGridPlugin from '@fullcalendar/timegrid';
import resourceTimeGridPlugin from '@fullcalendar/resource-timegrid';
import { Events, Resources } from 'data';
import * as moment from 'moment';
import {CalendarApi, CalendarOptions, EventApi, EventChangeArg, EventMountArg} from '@fullcalendar/core';
import {Appointment, getStateStylesClass} from '../../../../shared/models/appointment.model';
import {Profile} from '../../../../shared/models/profile.model';
import {pages} from '../../../../shared/config/pages';
@Component({
  selector: 'app-appointments-calendar',
  templateUrl: './appointments-calendar.component.html',
  styleUrls: ['./appointments-calendar.component.scss']
})
export class AppointmentsCalendarComponent implements OnInit, OnChanges, AfterViewInit {

  @Input() type: 'DAILY' | 'WEEKLY' | 'MONTHLY' = 'DAILY';
  @Input() date!: moment.Moment;
  @Input() appointments!: Appointment[];
  @Input() inProgressAppointments: Appointment[] = [];

  @Input() allowedDoctors: Profile[] = [];

  @Input() selectedDoctors: string[] = [];

  @Output() dayClickEvent: EventEmitter<Date> = new EventEmitter<Date>();

  allAppointments: Appointment[] = [];
  currentDate: Date = new Date();
  eventCalendar: any[] = Events;
  resourcesArr: any[] = [];
  calendar: CalendarApi | undefined = undefined;
  showModal: boolean = false;
  showModalConfirmation: boolean = false;
  calendarOptions: CalendarOptions = {};
  appointmentStatus: boolean = true;
  meetingStatus: boolean = true;
  selectedEvent: EventApi | undefined = undefined; // CURRENT EVENT SELECTED BY THE USER
  groupedArr: any = {};
  colors: any[] = [];
  pages = pages;


  @Output() appointmentClick = new EventEmitter<Appointment>();
  dateSuffix: string;
  private viewSet: boolean;


  constructor(private renderer: Renderer2) {

  }

  ngOnInit(): void {
    this.changeDate(moment(this.date).toISOString());
    this.setAllAppointments();
    this.setResources();
    this.calendarOptions = {
      schedulerLicenseKey: 'CC-Attribution-NonCommercial-NoDerivatives',
      initialView: this.viewType,
      plugins: [
        dayGridPlugin,
        resourceTimeGridPlugin,
        timeGridPlugin,
        interactionPlugin],
      firstDay: 1,
      eventBorderColor: 'transparent',
      eventBackgroundColor: 'transparent',
      initialDate: moment(this.date).toISOString(),
      locale: 'fr', // INITIAL LANG OF THE CALENDER YOU CAN SPECIFY MORE THAN ONE (CHECK FOR LOCALES:[] PROPRIETY)
      editable: false,
      displayEventTime: false,
      stickyHeaderDates: true,
      resources: this.resourcesArr,
      dayMaxEvents: true, // ALLOW "more" LINK WHEN TOO MANY EVENTS
      nowIndicator: true,
      slotDuration: '00:10:00', // CONTROL HOW YOU CAN DIVID YOUR DAY (00:25:00) MEANING EACH ROW IN DAY VIEW EQUALS TO 25MIN.
      slotLabelFormat: { hour: 'numeric', minute: '2-digit', omitZeroMinute: true, hour12: false },
      allDaySlot: false,
      datesAboveResources: true, // IN resourceDayGrid view (DAY VIEW) should render their date headings above their resource headings.
      headerToolbar: {
        left: '',
        center: 'title',
        right: ''
        // right: ''
      },

      eventDrop: this.changeEventHandler.bind(this),
      eventResize: this.changeEventHandler.bind(this),
      selectable: true,
      height: 900, // CONTROL HEIGHT OF THE CALENDAR IN YOUR PAGE.

      eventDidMount: (event: EventMountArg) => {
        this.calendar = event.view.calendar;
      },
      dayCellDidMount: this.cellDidMount.bind(this),
      select: this.selectionHandler.bind(this),
      eventClick: this.showModalDialog.bind(this),
      eventChange: this.changeEventHandler.bind(this),
      dateClick: this.cellClickHandler.bind(this),
    };
  }

  init() {

  }

  ngOnChanges(changes: any): void {
    this.setAllAppointments();
    if (changes.selectedDoctors?.currentValue) {
      this.setResources();
    }
    if (changes.selectedDoctors?.currentValue) {
      this.dateSuffix = this.date.format('YYYY-MM-');
    }
    this.groupedArr = this.groupBy(this.allAppointments, (v: Appointment) => (moment(v.date).format('YYYY-MM-DD')));
    if (changes.appointments?.currentValue || changes.inProgressAppointments?.currentValue) {
      this.allAppointments.forEach((appointment: any, index: number) => {
        appointment.start = appointment.startTime;
        appointment.end = moment(appointment.startTime).clone().add('30', 'minutes').toDate();
        appointment.className = 'eventItem';
        appointment.resourceId = appointment.doctor?._id;
      });

      if (this.allAppointments.length === 0) {
        this.allAppointments = [{ startTime: moment().toDate()}];
      }
      this.calendar?.removeAllEvents();
      this.calendar?.addEventSource(this.allAppointments);
    }

    if (changes.date?.currentValue) {
      this.changeDate(this.date.toDate() || new Date());
    }

    if (changes.type?.currentValue){
      this.changeView(this.viewType);
    }
    // Called before any other lifecycle hook. Use it to inject dependencies, but avoid any serious work here.
    // Add '${implements OnChanges}' to the class.
  }
  ngAfterViewInit() {
    this.changeView(this.viewType);
  }


  setAllAppointments() {
    this.allAppointments = [...this.appointments, ...this.inProgressAppointments];
  }
  setResources() {
    this.resourcesArr = this.selectedDoctors.map((doctorId: string) => {
      const selectedDoctor = this.allowedDoctors.find((doctor: Profile) => doctor._id === doctorId);
      return {
        id: doctorId,
        title: (selectedDoctor?.firstName || '') + ' ' + (selectedDoctor?.lastName || '')
      };
    });
    this.calendar?.setOption('resources', this.resourcesArr);
  }
  /**
   *  FIRED WHEN YOU CHANGE EVENT POSITION OR RESIZE TIME OF ANY EVENT.
   *  @PARAM :EVENT ==> CONTAINS DATA ABOUT LAST POSITION, AND THE NEW ONE.
   */
  changeEventHandler({ revert, event}: EventChangeArg): void {

  }

  /**
   *  CONTROL DATE CHANGE IN CALENDAR (SYNC BETWEEN p-calendar with fullcalendar COMPONENT ).
   */
  changeDate(date: any): void {
    this.calendar?.gotoDate(moment(date).toISOString());
    this.calendar?.select(moment(date).toISOString());
  }

  /*dyanamiclly change calendar view
    @params view: ['dayGridMonth' ==> month view | 'timeGridWeek' ==> Week view ]
  */
  changeView(view: string) {
    this.calendar?.changeView(view);
  }

  /**
   *  TOGGLE DIALOG COMPONENT
   */
  showModalDialog(event: any) {
    this.showModal = true;
    this.selectedEvent = event.event;
  }

  /**
   * CALLED WHEN THE USER SELECT ONE OR MAMY CELLS INSIDE CALENDAR (USED TO DETECT MULTISELECTION )
   */
  selectionHandler(event: any): void {

  }

  /**
   * CALLED WHEN THE USER CLICK OR (JUST SINGLE SELECTION) ON ANY CELL INSIDE CALENDAR
   */
  cellClickHandler(event: any): void {
  }


  toggleAppointmentVisibility(event: any): void {
    this.appointmentStatus = event.checked;
    if (!this.appointmentStatus) {
      this.calendarOptions.events = this.eventCalendar.filter((ev) => ev.type !== 'medical');
    } else {
      this.calendarOptions.events = this.eventCalendar;
    }
  }

  toggleMeetingVisibility(event: any) {
    if (!this.meetingStatus) {
      this.calendarOptions.events = this.eventCalendar.filter((av) => av.type !== 'others');
      this.meetingStatus = event.checked;
    } else {
      this.calendarOptions.events = this.eventCalendar;
    }

  }


  cellDidMount(event: any): void {
    const itemIndex = Object.keys(this.groupedArr).indexOf(moment(event.date).format('YYYY-MM-DD'));

    if (itemIndex !== -1) {
      for (const [key, val] of Object.entries(this.groupedArr) as any) {
        if (key === moment(event.date).format('YYYY-MM-DD')) {
          const h2el = this.renderer.createElement('span');
          const h2Text = this.renderer.createText(`${val.length}`);
          this.renderer.setStyle(h2el, 'background', val[0].backgroundColor);
          if (!this.colors.includes(val[0].backgroundColor)){
            this.colors.push(val[0].backgroundColor);
          }
          this.renderer.appendChild(h2el, h2Text);
          this.renderer.setAttribute(h2el, 'class', val[0].className + ' inner-badge');
          event.el.firstChild?.appendChild(h2el);
        }
      }
    }
  }


  groupBy(arr: Appointment[], key: any): Map<typeof key, Appointment[]> {
    return arr.reduce((a: any, b: any, i: any) => ((a[key(b, i, arr)] ||= []).push(b), a), {});
  }

  get viewType(): string {
    switch (this.type) {
      case 'MONTHLY':
        return 'dayGridMonth';
      case 'WEEKLY':
        return 'resourceTimeGridWeek';
        case 'DAILY':
          return 'timeGridDay';
    }
  }

  handleAppointmentClick(appointment: any) {
    // if(this.openedAppointmentId !== appointment._id){
    //   this.openedAppointmentId = appointment._id;
    // } else {
    //   this.openedAppointmentId = '';
    // }
    const clickedAppointment = this.allAppointments.find((app: Appointment) => appointment._id === app._id);
    this.appointmentClick.emit(clickedAppointment);
  }

  getDayCellContent(arg: any): void {
  }

  handleDayClick(date: Date) {
    this.dayClickEvent.emit(date);

  }
}
