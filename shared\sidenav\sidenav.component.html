<div
  class="sidenav-container"
  [ngClass]="{ collapse: !fullSideNav }"
  fxLayout="column"
  fxLayoutAlign="start center"
>
  <div
    class="list-icon-container"
    fxLayoutAlign="center"
    (click)="toggleSideNavModes()"
  >
    <mat-icon aria-hidden="false" aria-label="left-arrow"
      >format_list_bulleted
    </mat-icon>
  </div>
  <div
    fxLayout="column"
    fxLayoutAlign="start center"
    [ngClass]="{ collapse: !fullSideNav }"
  >
    <img src="assets/logos/winplusmed.svg" class="sidenav-logo" alt="logo" />

    <app-labeled-avatar
      [profile]="connectedUser.profile"
      [label]="connectedUser.profile?.title"
      [collapse]="!fullSideNav"
    ></app-labeled-avatar>
  </div>
  <app-seperator></app-seperator>

  <mat-selection-list [multiple]="false">
    <div *ngFor="let page of pages; let i = index">
      <mat-list-option
        [value]="page.pageTitle"
        *ngIf="((!page.access || isAllowed(page)) && !page.onlyAdmin) || (page.onlyAdmin && connectedUser.profile?.isAdmin)"
      >
        <app-sidenav-item
          [dir]="dir"
          [collapsed]="!fullSideNav"
          [routerLink]="page.link"
          [selected]="isSelected(page.link)"
          [title]="page.pageTitle | translate"
          [icon]="page.icon"
          [BadgeNumber]="getBadgeNumber(page.link).number"
          [BadgeColor]="getBadgeNumber(page.link).color"
        ></app-sidenav-item>
      </mat-list-option>
    </div>

    <!--    <mat-list-option value="disconnect" (click)="disconnect()">-->
    <!--      <app-sidenav-item-->
    <!--        [collapsed]="!fullSideNav"-->
    <!--        title="Déconnecter"-->
    <!--        icon="log-out.svg"-->
    <!--      ></app-sidenav-item>-->
    <!--    </mat-list-option>-->
  </mat-selection-list>

  <div *ngIf="fullSideNav" class="time-container">
    <span>{{ now | date: 'dd/MM/yyyy HH:mm' }}</span>

    <p class="mt-2 mb-0 small text-opacity-75" >
    Sophalia Med
    &copy; {{ today | date: 'yyyy' }} <br>

    <a href="https://sophatel.com" target="_blank" class="external-link fw-bold text-decoration-none fs-4">
        Sophatel Ingénierie
    </a>
    <br> Tous droits réservés.
</p>


  </div>
</div>
