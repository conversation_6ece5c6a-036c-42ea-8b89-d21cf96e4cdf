import { Injectable } from '@angular/core';

import pdfMake from 'pdfmake/build/pdfmake';
import pdfFonts from 'pdfmake/build/vfs_fonts';
import {PrescriptionPage} from '../models/prescription-page.model';
import {User} from '../models/user.model';
import {StorageService} from '../../core/services/storage.service';
import {Session} from '../models/session.model';
import * as moment from 'moment';
import {Observable} from 'rxjs';
import {DecimalPipe} from '@angular/common';
pdfMake.vfs = pdfFonts.vfs;

export type PrescriptionType = 'ORDONNANCE' | 'RADIOLOGIE' | 'BIOLOGIE' | 'AUTRE';

@Injectable({
  providedIn: 'root'
})
export class ExportPrescriptionService {

  user!: User;
  constructor(private storageService: StorageService, private decimalPipe: DecimalPipe) {
    this.user = this.storageService.getUser();
  }


  exportPrescription(prescriptionPage: PrescriptionPage, session: Session) {
    const printHeader = this.storageService.getUser().profile?.hospital?.prescriptionHeader;
    const header = this.getHeader(prescriptionPage, session, printHeader);
    const footer = printHeader ? this.getFooter() : [];
    const pdfStructure: any = {
      pageSize: 'A5',
      ...footer,
      content: [
        ...header,
        ...this.getBody(prescriptionPage) as any,
        // prescriptionPage.type === 'ORDONNANCE' ? {
        //   text: 'Total: ' + this.totalPrescription(prescriptionPage) +' DH', bold: true, alignment: 'right', fontSize: 16, margin: [0, 20, 0, 0]
        // } : {}
      ],
      ...this.getStyles()
    };
    pdfMake.createPdf(pdfStructure).download(this.getFileName(prescriptionPage, session, true));
  }

  exportMultiplePrescriptions(prescriptionPages: PrescriptionPage[], session: Session, print = false) {
    const printHeader = this.storageService.getUser().profile?.hospital?.prescriptionHeader;
    return new Observable((observer) => {
      setTimeout(() => {
        const footer = printHeader ? this.getFooter() : [];
        const pdfStructure: any = {
          ...footer,
          content: prescriptionPages.map((prescriptionPage, index) => {
            const header = this.getHeader(prescriptionPage, session, printHeader);

            return [
              index !== 0 ? {text: '', pageBreak: 'before'} : {},
              ...header,
              ...this.getBody(prescriptionPage) as any,
              // prescriptionPage.type === 'ORDONNANCE' ? {
              //   text: 'Total: ' + this.totalPrescription(prescriptionPage) +' DH', bold: true, alignment: 'right', fontSize: 16, margin: [0, 20, 0, 0]
              // } : {}
            ];
          }),
          ...this.getStyles()
        };
        if (print){
          pdfMake.createPdf(pdfStructure).print();
        } else {
          pdfMake.createPdf(pdfStructure).download(this.getFileName(prescriptionPages[0], session, true));
        }
        observer.next();
        observer.complete();
      }, 0);

  });
  }
  getHeader(prescriptionPage: PrescriptionPage, session: Session, printHeader: any){
    return [
      printHeader ? {
        table : {
          headerRows : 1,
          widths: [340.1],
          body : [
            [''],
            ['']
          ]
        },
        layout : 'headerLineOnly'
      } : [],
      printHeader ? {
        text: prescriptionPage.type,
        fontSize: 20,
        alignment: 'center'
      } : [],
      printHeader ? {
        table : {
          headerRows : 1,
          widths: [340.1],
          body : [
            [''],
            ['']
          ]
        },
        layout : 'headerLineOnly'
      } : [],
      !printHeader ? {
        text: '',
        margin: [0, 30, 0, 0]
      } : [],

      {
        columns: [

          [
            {text: [{text: 'patient ', color: '#aaaaab'}, session.patient.firstName + ' ' + session.patient.lastName],
              color: 'gray',
              bold: true,
              fontSize: 12,
              alignment: 'left',
              margin: [0, 20, 0, 0]},

            {text: [{text: 'date ', color: '#aaaaab'}, moment(session.startTime).format('DD/MM/yyyy')],
              color: 'gray',
              bold: true,
              fontSize: 12,
              alignment: 'left',
              margin: [0, 0, 0, 20]}
          ],
          printHeader ? [{
            text: this.user.profile?.hospital?.name,
            color: 'gray',
            bold: true,
            fontSize: 12,
            alignment: 'right',
            margin: [0, 20, 0, 0],
          },
            {
              text: this.user.profile?.hospital?.phoneNumber,
              color: 'gray',
              bold: true,
              fontSize: 12,
              alignment: 'right',
              margin: [0, 0, 0, 20],
            }] : []
        ],
      },
      {
        text: prescriptionPage.title,
        bold: true,
        margin: [10, 20, 0, 0],
        fontSize: 12,
        alignment: 'center'
      },
    ];
  }

  getBody(prescriptionPage: PrescriptionPage) {
    return prescriptionPage.items?.map((prescriptionItem, i) => {
      const price = prescriptionItem.price || 0;
      const quantity = prescriptionItem.quantity || 0;

      let titleObject: any = {text: prescriptionItem.name, decoration: 'underline', bold: true};
      const itemTotal = (prescriptionItem.price || 0) * (prescriptionItem.quantity || 0);
      if (prescriptionPage.type === 'ORDONNANCE') {
        // Handle quantity display logic
        let displayText: string;
        if (quantity === 0) {
          // When quantity is 0, show only the product name
          displayText = prescriptionItem.name;
        } else {
          // When quantity is 1 or more, show "quantity x product name"
          displayText = prescriptionItem.quantity + ' x ' + prescriptionItem.name;
        }

        titleObject = {
          columns: [
            {text: displayText, decoration: 'underline', bold: true},
            // {text: itemTotal ? (this.decimalPipe.transform(itemTotal, '1.2-2') + ' DH') : '', fontSize: 12, alignment: 'right'}
          ]
        };
      }
      if(prescriptionPage.type !== 'AUTRE') {
        return [{
          margin: [10, (i === 0 ? 10 : 5), 0, 10],
          fontSize: 14,
          ul: [
            [
              titleObject,
              prescriptionItem.notes?.map((note, index) => {
                return {text: '- ' + note, margin: [10, index === 0 ? 5 : 3, 0, 0], fontSize: 14};
              })
            ]
          ]
        }];
      } else {
        return {
          text: prescriptionItem.name,
          alignment: 'justify',
          margin: [ 0, 0, 0, 10 ]
        };
      }

    });
  }

  getFooter(){
    const hospital = this.user.profile?.hospital;
    return {
      footer: {
        alignment: 'center',
        columns: [
          [{
            table : {
              margin: [20, 0, 0, 0],
              headerRows : 1,
              borderColor: '#aaaaab',
              widths: [426],
              body : [
                [''],
                ['']
              ]
            },
            layout : 'headerLineOnly',
          },
            {
              text: 'Addresse: ' + hospital?.address
            }]
        ]
      },
    };
  }

  totalPrescription(prescriptionPage: PrescriptionPage) {
    return this.decimalPipe.transform(prescriptionPage.items?.reduce((prev, curr) => {
      return prev + (curr.quantity || 0) * (curr.price || 0);
    }, 0), '1.2-2');
  }

  getFileName(prescriptionPage: PrescriptionPage, session: Session, multi: boolean) {
    return prescriptionPage.type +  (!multi ? ('_' + prescriptionPage.title) : '') + '_PATIENT-' + session.patient.firstName + '-' + session.patient.lastName + '_' + moment(session.date).format('DD-MM-yyyy');
  }

  getStyles(){
    return {
      styles: {
        // Document Header
        documentHeaderLeft: {
          fontSize: 10,
          margin: [5, 5, 5, 5],
          alignment: 'left'
        },
        documentHeaderCenter: {
          fontSize: 10,
          margin: [5, 5, 5, 5],
          alignment: 'center'
        },
        documentHeaderRight: {
          fontSize: 10,
          margin: [5, 5, 5, 5],
          alignment: 'right'
        },
        // Document Footer
        documentFooterLeft: {
          fontSize: 10,
          margin: [5, 5, 5, 5],
          alignment: 'left'
        },
        documentFooterCenter: {
          fontSize: 10,
          margin: [5, 5, 5, 5],
          alignment: 'center'
        },
        documentFooterRight: {
          fontSize: 10,
          margin: [5, 5, 5, 5],
          alignment: 'right'
        },
        // Invoice Title
        invoiceTitle: {
          fontSize: 22,
          bold: true,
          alignment: 'right',
          margin: [0, 0, 0, 15]
        },
        // Invoice Details
        invoiceSubTitle: {
          fontSize: 12,
          alignment: 'right'
        },
        invoiceSubValue: {
          fontSize: 12,
          alignment: 'right'
        },
        // Billing Headers
        invoiceBillingTitle: {
          fontSize: 14,
          bold: true,
          alignment: 'left',
          margin: [0, 20, 0, 5],
        },
        // Billing Details
        invoiceBillingDetails: {
          alignment: 'left'

        },
        invoiceBillingAddressTitle: {
          margin: [0, 7, 0, 3],
          bold: true
        },
        invoiceBillingAddress: {

        },
        // Items Header
        itemsHeader: {
          margin: [0, 5, 0, 5],
          bold: true
        },
        // Item Title
        itemTitle: {
          bold: true,
        },
        itemSubTitle: {
          italics: true,
          fontSize: 11
        },
        itemNumber: {
          margin: [0, 5, 0, 5],
          alignment: 'center',
        },
        itemTotal: {
          margin: [0, 5, 0, 5],
          bold: true,
          alignment: 'center',
        },

        // Items Footer (Subtotal, Total, Tax, etc)
        itemsFooterSubTitle: {
          margin: [0, 5, 0, 5],
          bold: true,
          alignment: 'right',
        },
        itemsFooterSubValue: {
          margin: [0, 5, 0, 5],
          bold: true,
          alignment: 'center',
        },
        itemsFooterTotalTitle: {
          margin: [0, 5, 0, 5],
          bold: true,
          alignment: 'right',
        },
        itemsFooterTotalValue: {
          margin: [0, 5, 0, 5],
          bold: true,
          alignment: 'center',
        },
        signaturePlaceholder: {
          margin: [0, 70, 0, 0],
        },
        signatureName: {
          bold: true,
          alignment: 'center',
        },
        signatureJobTitle: {
          italics: true,
          fontSize: 10,
          alignment: 'center',
        },
        notesTitle: {
          fontSize: 10,
          bold: true,
          margin: [0, 50, 0, 3],
        },
        notesText: {
          fontSize: 10
        },
        center: {
          alignment: 'center',
        },
      },
      defaultStyle: {
        columnGap: 20,
      }
    };
  }
}
