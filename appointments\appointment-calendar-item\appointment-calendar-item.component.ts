import {
  Component,
  EventEmitter,
  Input,
  OnD<PERSON>roy,
  OnInit,
  Output
} from '@angular/core';
import {Appointment} from '../../../../shared/models/appointment.model';
import {getStateStylesClass} from '../../../../shared/models/appointment.model';
import {SocketService} from '../../../../core/services/socket.service';

@Component({
  selector: 'app-appointment-calendar-item',
  templateUrl: './appointment-calendar-item.component.html',
  styleUrls: ['./appointment-calendar-item.component.scss']
})
export class AppointmentCalendarItemComponent implements OnInit, OnDestroy {

  // @ViewChild('timeoffsOptionsRef') timeoffsOptionsRef: ElementRef;

  // @HostListener('document:click', ['$event'])
  // clickout(event: any) {
  //   if (!this.timeoffsOptionsRef.nativeElement.contains(event.target)) {
  //     this.showAppointment = false;
  //   }
  // }

  @Output() appointmentClick = new EventEmitter<Appointment>();
  @Input() appointment!: Appointment;
  getStateStylesClass = getStateStylesClass;
  showAppointment: boolean = false

  constructor(private socketService: SocketService) { }

  ngOnInit(): void {
    this.initSocketListner();
  }

  initSocketListner() {
    if (this.appointment._id) {
      this.socketService.listen(this.appointment._id).subscribe((res: any) => {
        if (res.appointment) {
          this.appointment = { ...this.appointment, ...res.appointment };
          // this.appointmentUpdatedEvent.emit(this.appointment);
        }
      });
    }
  }
  handleAppointmentClick() {
    // this.showAppointment = !this.showAppointment;
    this.appointmentClick.emit(this.appointment);
  }

  ngOnDestroy(): void {
    this.socketService.removeAllListeners(this.appointment._id + '');
  }
}
