.terms-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 2rem;
  font-family: '<PERSON>o', sans-serif;
  line-height: 1.6;
  color: #333;

  .header-section {
    text-align: center;
    margin-bottom: 3rem;
    padding-bottom: 2rem;
    border-bottom: 2px solid #e0e0e0;

    .main-title {
      font-size: 2.5rem;
      font-weight: 700;
      color: #2c3e50;
      margin-bottom: 1rem;
    }

    .subtitle {
      font-size: 1.2rem;
      color: #7f8c8d;
      font-weight: 300;
    }
  }

  .content-section {
    .section {
      margin-bottom: 2.5rem;
      padding: 1.5rem;
      background: #f8f9fa;
      border-radius: 8px;
      border-left: 4px solid #3498db;

      h2 {
        font-size: 1.5rem;
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 1rem;
      }

      p {
        font-size: 1rem;
        margin-bottom: 1rem;
        text-align: justify;
      }

      ul {
        margin: 1rem 0;
        padding-left: 2rem;

        li {
          margin-bottom: 0.5rem;
          font-size: 1rem;
        }
      }

      &.highlight-section {
        background: #fff3cd;
        border-left-color: #ffc107;

        h2 {
          color: #856404;
        }

        p {
          color: #856404;
          font-weight: 500;
        }
      }
    }
  }

  .footer-section {
    margin-top: 3rem;
    padding-top: 2rem;
    border-top: 2px solid #e0e0e0;
    text-align: center;

    .last-updated {
      font-size: 0.9rem;
      color: #7f8c8d;
      margin-bottom: 1rem;
    }

    .contact-info {
      font-size: 1rem;
      color: #2c3e50;
      font-weight: 500;
    }
  }
}

// RTL Support
[dir="rtl"] .terms-container {
  .content-section .section {
    border-left: none;
    border-right: 4px solid #3498db;

    &.highlight-section {
      border-right-color: #ffc107;
    }

    ul {
      padding-left: 0;
      padding-right: 2rem;
    }
  }
}

// Responsive Design
@media (max-width: 768px) {
  .terms-container {
    padding: 1rem;

    .header-section {
      .main-title {
        font-size: 2rem;
      }

      .subtitle {
        font-size: 1rem;
      }
    }

    .content-section .section {
      padding: 1rem;

      h2 {
        font-size: 1.3rem;
      }

      p, li {
        font-size: 0.9rem;
      }
    }
  }
}
