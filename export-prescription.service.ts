import { Injectable } from '@angular/core';

import pdfMake from 'pdfmake/build/pdfmake';
import pdfFonts from 'pdfmake/build/vfs_fonts';
import {PrescriptionPage} from '../models/prescription-page.model';
import {User} from '../models/user.model';
import {StorageService} from '../../core/services/storage.service';
import {Session} from '../models/session.model';
import * as moment from 'moment';
import {Observable} from 'rxjs';
import {DecimalPipe} from '@angular/common';
pdfMake.vfs = pdfFonts.vfs;

export type PrescriptionType = 'ORDONNANCE' | 'RADIOLOGIE' | 'BIOLOGIE' | 'AUTRE';

@Injectable({
  providedIn: 'root'
})
export class ExportPrescriptionService {

  user!: User;
  constructor(private storageService: StorageService, private decimalPipe: DecimalPipe) {
    this.user = this.storageService.getUser();
  }


  exportPrescription(prescriptionPage: PrescriptionPage, session: Session) {
    const printHeader = this.storageService.getUser().profile?.hospital?.prescriptionHeader;
    const header = this.getModernHeader(prescriptionPage, session, printHeader);
    const footer = printHeader ? this.getModernFooter() : {};
    const pdfStructure: any = {
      pageSize: 'A4',
      pageMargins: [40, 60, 40, 80],
      ...footer,
      content: [
        ...header,
        ...this.getModernBody(prescriptionPage) as any,
        this.getSignatureSection(session)
      ],
      ...this.getModernStyles()
    };
    pdfMake.createPdf(pdfStructure).download(this.getFileName(prescriptionPage, session, true));
  }

  exportMultiplePrescriptions(prescriptionPages: PrescriptionPage[], session: Session, print = false) {
    const printHeader = this.storageService.getUser().profile?.hospital?.prescriptionHeader;
    return new Observable((observer) => {
      setTimeout(() => {
        const footer = printHeader ? this.getModernFooter() : {};
        const pdfStructure: any = {
          pageSize: 'A4',
          pageMargins: [40, 60, 40, 80],
          ...footer,
          content: prescriptionPages.map((prescriptionPage, index) => {
            const header = this.getModernHeader(prescriptionPage, session, printHeader);

            return [
              index !== 0 ? {text: '', pageBreak: 'before'} : {},
              ...header,
              ...this.getModernBody(prescriptionPage) as any,
              this.getSignatureSection(session)
            ];
          }),
          ...this.getModernStyles()
        };
        if (print){
          pdfMake.createPdf(pdfStructure).print();
        } else {
          const firstPage = prescriptionPages && prescriptionPages.length > 0 ? prescriptionPages[0] : { type: 'ORDONNANCE' as PrescriptionType, title: '', items: [] };
          pdfMake.createPdf(pdfStructure).download(this.getFileName(firstPage, session, true));
        }
        observer.next();
        observer.complete();
      }, 0);

  });
  }

  // Modern Professional Header
  getModernHeader(prescriptionPage: PrescriptionPage, session: Session, printHeader: any) {
    const hospital = this.user.profile?.hospital;
    const prescriptionDate = moment(session.startTime).format('DD/MM/YYYY');

    if (!printHeader) {
      // Minimal header when prescriptionHeader is disabled
      return [
        {
          text: '',
          margin: [0, 30, 0, 0] // Add top spacing
        },
        {
          columns: [
            {
              width: '*',
              stack: [
                {
                  text: [
                    { text: 'Ordonnance N°: ', color: '#666666', bold: true },
                    { text: `${this.generatePrescriptionNumber(session)}`, color: '#000000', bold: true }
                  ],
                  fontSize: 12,
                  margin: [0, 0, 0, 8]
                },
                {
                  text: [
                    { text: 'Patient: ', color: '#666666', bold: true },
                    { text: `${session.patient.firstName} ${session.patient.lastName}`, color: '#000000' }
                  ],
                  fontSize: 12,
                  margin: [0, 0, 0, 8]
                },
                {
                  text: [
                    { text: 'Date: ', color: '#666666', bold: true },
                    { text: prescriptionDate, color: '#000000' }
                  ],
                  fontSize: 12,
                  margin: [0, 0, 0, 20]
                }
              ]
            }
          ]
        }
      ];
    }

    // Full header when prescriptionHeader is enabled
    return [
      // Hospital Header Section
      {
        table: {
          widths: ['*', 'auto'],
          body: [
            [
              {
                stack: [
                  {
                    text: hospital?.name || 'Cabinet Médical',
                    style: 'hospitalName'
                  },
                  {
                    text: hospital?.address || '',
                    style: 'hospitalAddress'
                  },
                  {
                    text: hospital?.phoneNumber || '',
                    style: 'hospitalPhone'
                  }
                ]
              },
              {
                stack: [
                  {
                    text: prescriptionPage?.type || 'ORDONNANCE',
                    style: 'prescriptionType'
                  },
                  {
                    text: `N° ${this.generatePrescriptionNumber(session)}`,
                    style: 'prescriptionNumber'
                  }
                ]
              }
            ]
          ]
        },
        layout: 'noBorders',
        margin: [0, 0, 0, 20]
      },

      // Divider Line
      {
        canvas: [
          {
            type: 'line',
            x1: 0, y1: 0,
            x2: 515, y2: 0,
            lineWidth: 2,
            lineColor: '#000000'
          }
        ],
        margin: [0, 0, 0, 20]
      },

      // Patient Information Section
      {
        table: {
          widths: ['*', '*'],
          body: [
            [
              {
                stack: [
                  {
                    text: 'PATIENT',
                    style: 'sectionTitle'
                  },
                  {
                    text: `${session.patient.firstName} ${session.patient.lastName}`,
                    style: 'patientName'
                  },
                  {
                    text: session.patient.email ? `Email: ${session.patient.email}` : '',
                    style: 'patientDetails'
                  }
                ]
              },
              {
                stack: [
                  {
                    text: 'DATE DE CONSULTATION',
                    style: 'sectionTitle'
                  },
                  {
                    text: prescriptionDate,
                    style: 'consultationDate'
                  }
                ]
              }
            ]
          ]
        },
        layout: 'noBorders',
        margin: [0, 0, 0, 30]
      },

      // Prescription Title
      {
        text: prescriptionPage?.title || prescriptionPage?.type || 'ORDONNANCE',
        style: 'prescriptionTitle',
        margin: [0, 0, 0, 20]
      }
    ];
  }

  // Modern Professional Body
  getModernBody(prescriptionPage: PrescriptionPage) {
    if (!prescriptionPage || !prescriptionPage.items || prescriptionPage.items.length === 0) {
      return [{
        text: 'Aucun élément prescrit',
        style: 'emptyMessage',
        margin: [0, 20, 0, 20]
      }];
    }

    const bodyContent: any[] = [];

    // Add prescription items
    prescriptionPage.items.forEach((prescriptionItem: any, index: number) => {
      const quantity = prescriptionItem.quantity || 0;

      // Item container
      const itemContainer = {
        table: {
          widths: ['*'],
          body: [
            [
              {
                stack: [
                  // Item header with quantity and name (price hidden)
                  {
                    text: this.formatItemTitle(prescriptionItem, quantity),
                    style: 'itemTitle'
                  },

                  // Item notes/instructions
                  prescriptionItem.notes && prescriptionItem.notes.length > 0 ? {
                    stack: prescriptionItem.notes.map((note: string) => ({
                      text: `• ${note}`,
                      style: 'itemNote'
                    })),
                    margin: [10, 5, 0, 0]
                  } : null
                ].filter(Boolean)
              }
            ]
          ]
        },
        layout: {
          hLineWidth: () => 0,
          vLineWidth: () => 0,
          paddingLeft: () => 15,
          paddingRight: () => 15,
          paddingTop: () => 10,
          paddingBottom: () => 10
        },
        margin: [0, index === 0 ? 0 : 10, 0, 0]
      };

      bodyContent.push(itemContainer);
    });

    return bodyContent;
  }

  // Helper method to format item title based on type and quantity
  private formatItemTitle(prescriptionItem: any, quantity: number): string {
    const itemName = prescriptionItem?.name || 'Produit sans nom';
    if (quantity === 0) {
      return itemName;
    } else {
      return `${quantity} x ${itemName}`;
    }
  }

  // Generate prescription number
  generatePrescriptionNumber(session: Session): string {
    const date = moment(session.startTime).format('YYYYMMDD');
    const sessionId = session._id?.slice(-6).toUpperCase() || 'XXXXXX';
    return `${date}-${sessionId}`;
  }

  // Modern Signature Section
  getSignatureSection(session: Session): any {
    const doctor = session.doctor;
    return {
      table: {
        widths: ['*', '*'],
        body: [
          [
            {
              text: '',
              border: [false, false, false, false]
            },
            {
              stack: [
                {
                  text: 'Signature du médecin',
                  style: 'signatureLabel',
                  margin: [0, 40, 0, 10]
                },
                {
                  canvas: [
                    {
                      type: 'line',
                      x1: 0, y1: 0,
                      x2: 150, y2: 0,
                      lineWidth: 1,
                      lineColor: '#cccccc'
                    }
                  ],
                  margin: [0, 0, 0, 10]
                },
                {
                  text: `Dr. ${doctor.firstName} ${doctor.lastName}`,
                  style: 'doctorName'
                }
              ],
              border: [false, false, false, false]
            }
          ]
        ]
      },
      layout: 'noBorders',
      margin: [0, 30, 0, 0]
    };
  }

  // Modern Footer
  getModernFooter(): any {
    const hospital = this.user.profile?.hospital;
    return {
      footer: (currentPage: number, pageCount: number) => {
        return {
          table: {
            widths: ['*'],
            body: [
              [
                {
                  stack: [
                    {
                      canvas: [
                        {
                          type: 'line',
                          x1: 0, y1: 0,
                          x2: 515, y2: 0,
                          lineWidth: 1,
                          lineColor: '#000000'
                        }
                      ],
                      margin: [0, 0, 0, 10]
                    },
                    {
                      columns: [
                        {
                          text: hospital?.address ? `Adresse: ${hospital.address}` : '',
                          style: 'footerText'
                        },
                        {
                          text: `Page ${currentPage} sur ${pageCount}`,
                          style: 'footerText',
                          alignment: 'right'
                        }
                      ]
                    }
                  ]
                }
              ]
            ]
          },
          layout: 'noBorders',
          margin: [40, 10, 40, 20]
        };
      }
    };
  }

  getHeader(prescriptionPage: PrescriptionPage, session: Session, printHeader: any){
    return [
      printHeader ? {
        table : {
          headerRows : 1,
          widths: [340.1],
          body : [
            [''],
            ['']
          ]
        },
        layout : 'headerLineOnly'
      } : [],
      printHeader ? {
        text: prescriptionPage.type,
        fontSize: 20,
        alignment: 'center'
      } : [],
      printHeader ? {
        table : {
          headerRows : 1,
          widths: [340.1],
          body : [
            [''],
            ['']
          ]
        },
        layout : 'headerLineOnly'
      } : [],
      !printHeader ? {
        text: '',
        margin: [0, 30, 0, 0]
      } : [],

      {
        columns: [

          [
            {text: [{text: 'patient ', color: '#aaaaab'}, session.patient.firstName + ' ' + session.patient.lastName],
              color: 'gray',
              bold: true,
              fontSize: 12,
              alignment: 'left',
              margin: [0, 20, 0, 0]},

            {text: [{text: 'date ', color: '#aaaaab'}, moment(session.startTime).format('DD/MM/yyyy')],
              color: 'gray',
              bold: true,
              fontSize: 12,
              alignment: 'left',
              margin: [0, 0, 0, 20]}
          ],
          printHeader ? [{
            text: this.user.profile?.hospital?.name,
            color: 'gray',
            bold: true,
            fontSize: 12,
            alignment: 'right',
            margin: [0, 20, 0, 0],
          },
            {
              text: this.user.profile?.hospital?.phoneNumber,
              color: 'gray',
              bold: true,
              fontSize: 12,
              alignment: 'right',
              margin: [0, 0, 0, 20],
            }] : []
        ],
      },
      {
        text: prescriptionPage.title,
        bold: true,
        margin: [10, 20, 0, 0],
        fontSize: 12,
        alignment: 'center'
      },
    ];
  }

  getBody(prescriptionPage: PrescriptionPage) {
    return prescriptionPage.items?.map((prescriptionItem, i) => {
      const price = prescriptionItem.price || 0;
      const quantity = prescriptionItem.quantity || 0;

      let titleObject: any = {text: prescriptionItem.name || 'Produit sans nom', decoration: 'underline', bold: true};
      const itemTotal = (prescriptionItem.price || 0) * (prescriptionItem.quantity || 0);
      if (prescriptionPage.type === 'ORDONNANCE') {
        // Handle quantity display logic
        let displayText: string;
        if (quantity === 0) {
          // When quantity is 0, show only the product name
          displayText = prescriptionItem.name || 'Produit sans nom';
        } else {
          // When quantity is 1 or more, show "quantity x product name"
          displayText = prescriptionItem.quantity + ' x ' + (prescriptionItem.name || 'Produit sans nom');
        }

        titleObject = {
          columns: [
            {text: displayText, decoration: 'underline', bold: true},
            // {text: itemTotal ? (this.decimalPipe.transform(itemTotal, '1.2-2') + ' DH') : '', fontSize: 12, alignment: 'right'}
          ]
        };
      }
      if(prescriptionPage.type !== 'AUTRE') {
        return [{
          margin: [10, (i === 0 ? 10 : 5), 0, 10],
          fontSize: 14,
          ul: [
            [
              titleObject,
              prescriptionItem.notes?.map((note, index) => {
                return {text: '- ' + note, margin: [10, index === 0 ? 5 : 3, 0, 0], fontSize: 14};
              })
            ]
          ]
        }];
      } else {
        return {
          text: prescriptionItem.name || 'Produit sans nom',
          alignment: 'justify',
          margin: [ 0, 0, 0, 10 ]
        };
      }

    });
  }

  getFooter(){
    const hospital = this.user.profile?.hospital;
    return {
      footer: {
        alignment: 'center',
        columns: [
          [{
            table : {
              margin: [20, 0, 0, 0],
              headerRows : 1,
              borderColor: '#aaaaab',
              widths: [426],
              body : [
                [''],
                ['']
              ]
            },
            layout : 'headerLineOnly',
          },
            {
              text: 'Addresse: ' + hospital?.address
            }]
        ]
      },
    };
  }

  totalPrescription(prescriptionPage: PrescriptionPage) {
    return this.decimalPipe.transform(prescriptionPage.items?.reduce((prev, curr) => {
      return prev + (curr.quantity || 0) * (curr.price || 0);
    }, 0), '1.2-2');
  }

  getFileName(prescriptionPage: PrescriptionPage, session: Session, multi: boolean) {
    const type = prescriptionPage?.type || 'ORDONNANCE';
    const title = prescriptionPage?.title || '';
    const patientFirstName = session?.patient?.firstName || 'Unknown';
    const patientLastName = session?.patient?.lastName || 'Patient';
    const sessionDate = session?.date || session?.startTime || new Date();

    return type + (!multi ? ('_' + title) : '') + '_PATIENT-' + patientFirstName + '-' + patientLastName + '_' + moment(sessionDate).format('DD-MM-yyyy');
  }

  // Modern Professional Styles
  getModernStyles() {
    return {
      styles: {
        // Header Styles - Black & White Theme
        hospitalName: {
          fontSize: 16,
          bold: true,
          color: '#000000',
          margin: [0, 0, 0, 5]
        },
        hospitalAddress: {
          fontSize: 10,
          color: '#666666',
          margin: [0, 0, 0, 2]
        },
        hospitalPhone: {
          fontSize: 10,
          color: '#666666',
          margin: [0, 0, 0, 0]
        },
        prescriptionType: {
          fontSize: 18,
          bold: true,
          color: '#000000',
          alignment: 'right',
          margin: [0, 0, 0, 5]
        },
        prescriptionNumber: {
          fontSize: 12,
          color: '#666666',
          alignment: 'right',
          margin: [0, 0, 0, 0]
        },

        // Section Styles
        sectionTitle: {
          fontSize: 10,
          bold: true,
          color: '#000000',
          margin: [0, 0, 0, 5],
          letterSpacing: 0.5
        },
        patientName: {
          fontSize: 14,
          bold: true,
          color: '#000000',
          margin: [0, 0, 0, 3]
        },
        patientDetails: {
          fontSize: 10,
          color: '#666666',
          margin: [0, 0, 0, 0]
        },
        consultationDate: {
          fontSize: 12,
          bold: true,
          color: '#000000',
          margin: [0, 0, 0, 0]
        },
        prescriptionTitle: {
          fontSize: 16,
          bold: true,
          color: '#000000',
          alignment: 'center',
          margin: [0, 0, 0, 20]
        },

        // Item Styles
        itemTitle: {
          fontSize: 12,
          bold: true,
          color: '#000000',
          margin: [0, 0, 0, 8]
        },
        itemNote: {
          fontSize: 10,
          color: '#333333',
          margin: [0, 2, 0, 0],
          lineHeight: 1.3
        },
        emptyMessage: {
          fontSize: 12,
          color: '#666666',
          alignment: 'center',
          italics: true
        },

        // Signature Styles
        signatureLabel: {
          fontSize: 10,
          color: '#666666',
          alignment: 'center'
        },
        doctorName: {
          fontSize: 12,
          bold: true,
          color: '#000000',
          alignment: 'center'
        },

        // Footer Styles
        footerText: {
          fontSize: 9,
          color: '#666666'
        }
      },
      defaultStyle: {
        fontSize: 11,
        lineHeight: 1.4
      }
    };
  }

  getStyles(){
    return {
      styles: {
        // Document Header
        documentHeaderLeft: {
          fontSize: 10,
          margin: [5, 5, 5, 5],
          alignment: 'left'
        },
        documentHeaderCenter: {
          fontSize: 10,
          margin: [5, 5, 5, 5],
          alignment: 'center'
        },
        documentHeaderRight: {
          fontSize: 10,
          margin: [5, 5, 5, 5],
          alignment: 'right'
        },
        // Document Footer
        documentFooterLeft: {
          fontSize: 10,
          margin: [5, 5, 5, 5],
          alignment: 'left'
        },
        documentFooterCenter: {
          fontSize: 10,
          margin: [5, 5, 5, 5],
          alignment: 'center'
        },
        documentFooterRight: {
          fontSize: 10,
          margin: [5, 5, 5, 5],
          alignment: 'right'
        },
        // Invoice Title
        invoiceTitle: {
          fontSize: 22,
          bold: true,
          alignment: 'right',
          margin: [0, 0, 0, 15]
        },
        // Invoice Details
        invoiceSubTitle: {
          fontSize: 12,
          alignment: 'right'
        },
        invoiceSubValue: {
          fontSize: 12,
          alignment: 'right'
        },
        // Billing Headers
        invoiceBillingTitle: {
          fontSize: 14,
          bold: true,
          alignment: 'left',
          margin: [0, 20, 0, 5],
        },
        // Billing Details
        invoiceBillingDetails: {
          alignment: 'left'

        },
        invoiceBillingAddressTitle: {
          margin: [0, 7, 0, 3],
          bold: true
        },
        invoiceBillingAddress: {

        },
        // Items Header
        itemsHeader: {
          margin: [0, 5, 0, 5],
          bold: true
        },
        // Item Title
        itemTitle: {
          bold: true,
        },
        itemSubTitle: {
          italics: true,
          fontSize: 11
        },
        itemNumber: {
          margin: [0, 5, 0, 5],
          alignment: 'center',
        },
        itemTotal: {
          margin: [0, 5, 0, 5],
          bold: true,
          alignment: 'center',
        },

        // Items Footer (Subtotal, Total, Tax, etc)
        itemsFooterSubTitle: {
          margin: [0, 5, 0, 5],
          bold: true,
          alignment: 'right',
        },
        itemsFooterSubValue: {
          margin: [0, 5, 0, 5],
          bold: true,
          alignment: 'center',
        },
        itemsFooterTotalTitle: {
          margin: [0, 5, 0, 5],
          bold: true,
          alignment: 'right',
        },
        itemsFooterTotalValue: {
          margin: [0, 5, 0, 5],
          bold: true,
          alignment: 'center',
        },
        signaturePlaceholder: {
          margin: [0, 70, 0, 0],
        },
        signatureName: {
          bold: true,
          alignment: 'center',
        },
        signatureJobTitle: {
          italics: true,
          fontSize: 10,
          alignment: 'center',
        },
        notesTitle: {
          fontSize: 10,
          bold: true,
          margin: [0, 50, 0, 3],
        },
        notesText: {
          fontSize: 10
        },
        center: {
          alignment: 'center',
        },
      },
      defaultStyle: {
        columnGap: 20,
      }
    };
  }
}
