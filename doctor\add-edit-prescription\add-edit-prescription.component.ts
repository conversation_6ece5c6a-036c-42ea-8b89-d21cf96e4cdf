import {Component, Inject, Input, OnInit} from '@angular/core';
import {MAT_DIALOG_DATA, MatDialog, MatDialogRef} from '@angular/material/dialog';
import {FlatTreeControl} from '@angular/cdk/tree';
import {MatTreeFlatDataSource, MatTreeFlattener} from '@angular/material/tree';
import {PrescriptionService} from '../../../../shared/services/prescription.service';
import {PrescriptionPage} from '../../../../shared/models/prescription-page.model';
import {group} from '@angular/animations';
import {Drug} from '../../../../shared/models/drug.model';
import {DrugDialogComponent} from '../../../../shared/components/drug-dialog/drug-dialog.component';
import {CALLS_TYPES} from '../../../../shared/constants/defaults.consts';
import {MatBottomSheet} from '@angular/material/bottom-sheet';
import {BiologieDialogComponent} from '../../../../shared/components/biologie-dialog/biologie-dialog.component';
import {RadiologieDialogComponent} from '../../../../shared/components/radiologie-dialog/radiologie-dialog.component';
import {SessionService} from '../../../../shared/services/session.service';
import {PrescriptionItem} from '../../../../shared/models/prescription-item.model';
import {Session} from '../../../../shared/models/session.model';
import {ExportPrescriptionService} from '../../../../shared/services/export-prescription.service';

export type PrescriptionType = 'ORDONNANCE' | 'RADIOLOGIE' | 'BIOLOGIE' | 'AUTRE';
export interface GroupPresc<T> {
  name: string;
  items: T[];
}
@Component({
  selector: 'app-add-edit-prescription',
  templateUrl: './add-edit-prescription.component.html',
  styleUrls: ['./add-edit-prescription.component.scss']
})
export class AddEditPrescriptionComponent implements OnInit {

  prescriptionPage: PrescriptionPage;
  action: 'CREATE' | 'EDIT' = 'CREATE';
  prescriptionType: PrescriptionType = 'AUTRE';

  groupedPrescriptionData: GroupPresc<Drug>[] = [] ;
  selectionData: any[] = [];
  private searchWord: string = '';
  isLoadingDrugs: boolean = false;
  constructor(private dialog: MatDialog, private bottomSheet: MatBottomSheet, private prescriptionsService: PrescriptionService,
              @Inject(MAT_DIALOG_DATA) public data: {editable: boolean, type: PrescriptionType, count: number, prescriptionPage: PrescriptionPage, sessionId: string, session: Session},
              private dialogRef: MatDialogRef<any>,
              private prescriptionService: PrescriptionService,
              private exportPrescriptionService: ExportPrescriptionService) {
    if (data.prescriptionPage) {
      this.prescriptionPage = JSON.parse(JSON.stringify(data.prescriptionPage));
      if (!this.prescriptionPage.items) {
        this.prescriptionPage.items = [];
      }
      this.action = (data?.prescriptionPage?._id) ? 'EDIT' : 'CREATE';
    }
    this.prescriptionType = data?.type;
  }
  ngOnInit() {
    this.initPage();
    this.getSelectableData();
    this.setTitle();
  }

  getSelectableData() {
    if (this.prescriptionType === 'ORDONNANCE') {
      this.getDrugs();
    }
    if (this.prescriptionType === 'RADIOLOGIE') {
      this.getRadiologies();
    }
    if (this.prescriptionType === 'BIOLOGIE') {
      this.getBiologies();
    }

  }

  initPage() {
    if (!this.prescriptionPage) {
      this.prescriptionPage = {
        title: '',
        type: this.prescriptionType,
        items: []
      };
    }
  }
  getDrugs() {
    this.isLoadingDrugs = true;
    this.prescriptionsService.getDrugs(this.searchWord).subscribe((data) => {
      const items = data['docs'] ?? [];
      this.groupBy(items, 'drugFamily', 'name');
      this.isLoadingDrugs = false;
    }, (error) => {
      this.isLoadingDrugs = false;
    });
  }
  getRadiologies() {
    this.prescriptionsService.getRadiologies(this.searchWord).subscribe((items) => {
      this.groupBy(items, 'radiographFamily', 'name');
    });
  }

  getBiologies() {
    this.prescriptionsService.getBiologies(this.searchWord).subscribe((items) => {
      this.selectionData = items;
    });
  }

  groupBy(items: any, objectName: string, field: string){
    const groupedItems = items.reduce( (r: any, a: any) => {
      const drugFamilyName = (a[objectName] && a[objectName][field]) || '0';
      r[drugFamilyName] = r[drugFamilyName] || [];
      r[drugFamilyName].push(a);
      return r;
    }, Object.create(null));

    const groupedData: GroupPresc<Drug>[] = [];
    for (const item in groupedItems){
      if (item) {
        groupedData.push({
          name: item, items: groupedItems[item]
        });
      }
    }
    this.groupedPrescriptionData = groupedData;
  }

  addPresc(presc: Drug) {
    this.prescriptionPage.items?.push({
      prescription: presc,
      name: presc.name,
      price: presc.price,
      [this.presName]: presc._id,
      notes: [],
      quantity: 0
    } as any);
  }

  trackByFn(index: number, note: string) {
    return index + note;
  }


  handleNoteChange(itemIndex: number, i: number, $event: any) {
      // @ts-ignore
      this.prescriptionPage.items[itemIndex].notes[i] = $event.target.value;
  }

  addNote(itemIndex: number, $event?: any) {
    if ($event) {
      $event.preventDefault();
    }
    // @ts-ignore
    this.prescriptionPage.items[itemIndex].notes.push('');
    setTimeout(() => {
      const element = document.getElementById(
        'item-' + itemIndex + '-' + ((this.prescriptionPage as any).items[itemIndex].notes?.length - 1).toString()
      );
      if (element) {
        element.focus();
      }
    }, 0);
  }

  deleteNote(itemIndex: number, i: number) {
    // @ts-ignore
    this.prescriptionPage.items[itemIndex].notes = this.prescriptionPage.items[itemIndex].notes.filter((note, index) => index !== i);
  }

  deletePrescriptionItem(itemIndex: number) {
    this.prescriptionPage.items = this.prescriptionPage.items?.filter((item, index) => index !== itemIndex);

  }

  handleSubmit() {
    if (!this.prescriptionPage.type) {
      this.prescriptionPage.type = this.prescriptionType;
    }

    const prescriptionPage = JSON.parse(JSON.stringify(this.prescriptionPage));
    this.updateBeforeSend(prescriptionPage);
    this.prescriptionService
      .createEditPrescriptionPage(prescriptionPage, this.data.sessionId, this.prescriptionType)
      .subscribe(result => {
        this.dialogRef.close(result);
      });
    // this.dialogRef.close(this.prescriptionPage);
  }

  updateBeforeSend(prescriptionPage: PrescriptionPage) {
    prescriptionPage.items?.forEach((item: any) => {
      if (item.prescription) {
        item.name = item.prescription.name;
        item[this.presName] = item.prescription._id;
      }
    });
  }
  get isNotValid() {
    return this.prescriptionPage?.title?.length === 0;
  }


  addCustomElement() {
    this.prescriptionPage.items?.push({
      name: '',
      quantity: 0,
      price: 0,
      notes: []
    } as any);
    setTimeout(() => {
      const element = document.getElementById(
        'presc-' + ((this.prescriptionPage.items as any)?.length - 1).toString()
      );
      if (element) {
        element.focus();
      }
    }, 0);
  }

  addProduct() {
    let openComp: any = DrugDialogComponent;
    let name: string = 'drug';
    if (this.prescriptionType === 'ORDONNANCE') {
      openComp = DrugDialogComponent;
      name = 'drug';

    } else if (this.prescriptionType === 'BIOLOGIE') {
      openComp = BiologieDialogComponent;
      name = 'bio';
    }
    else if (this.prescriptionType === 'RADIOLOGIE') {
      openComp = RadiologieDialogComponent;
      name = 'radio';
    }
    const dialogRef = this.bottomSheet.open(openComp, {
      data: {
        [name]: {},
        type: CALLS_TYPES.create
      },
    });

    dialogRef.afterDismissed().subscribe((product: Drug) => {
      this.getSelectableData();
    }, () => null);
  }

  get presName() {
    let name: string = 'drug';
    if (this.prescriptionType === 'ORDONNANCE') {
      name = 'drug';

    } else if (this.prescriptionType === 'BIOLOGIE') {
      name = 'biologie';
    }
    else if (this.prescriptionType === 'RADIOLOGIE') {
      name = 'radiograph';
    }
    return name;
  }
  get addSuffix(): string {
    switch (this.prescriptionType) {
      case 'ORDONNANCE':
        return 'médecine';
      case 'RADIOLOGIE':
        return 'radiologie';
      case 'BIOLOGIE':
        return 'biologie';
      default:
        return '';
    }
  }
  get isGroupable() {
    return this.prescriptionType === 'ORDONNANCE' || this.prescriptionType === 'RADIOLOGIE';
  }
  get hasSelection() {
    return this.prescriptionType !== 'AUTRE';
  }

  handlePrint() {
    this.exportPrescriptionService.exportPrescription(this.prescriptionPage, this.data.session);
  }

  handleSearch($event: any) {
    this.searchWord = $event.target.value as string;
    this.getSelectableData();
  }

  get total() {
    return this.prescriptionPage.items?.reduce((prev, curr) => {
      return prev + (curr.quantity || 0) * (curr.price || 0);
    }, 0);
  }

  private setTitle() {
    if (this.action === 'CREATE') {
      const type = this.prescriptionPage.type?.toLocaleLowerCase() || '';
      const title = type.charAt(0).toLocaleUpperCase() + type.slice(1);
      this.prescriptionPage.title = `${title} ${this.data.count + 1}`;
    }
  }

  handleEscape($event: Event) {
    $event.preventDefault();
  }

  editText(spInput: any, selectedPresc: any, textContent: string) {

    spInput.innerText = textContent;
    selectedPresc.name = textContent;
  }
}
