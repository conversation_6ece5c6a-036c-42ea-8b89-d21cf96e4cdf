<div class="pdf-container" *ngIf="invoice">
  <div class="invoice-container" [id]="'invoice-container' + appointment?._id">
    <meta http-equiv="Content-Type" content="text/html;charset=UTF-8" />

    <div class="invoice-content">
      <!-- Header with conditional display based on prescriptionHeader setting -->
      <header class="modern-header" *ngIf="currentUser?.profile?.hospital?.prescriptionHeader">
        <!-- Top Section with Invoice Title and Number -->
        <div class="header-top">
          <div class="invoice-title">
            <h1>{{ 'invoice.invoice' | translate }}</h1>
            <div class="invoice-number">
              <span class="invoice-label">N°</span>
              <span class="invoice-id">{{ getInvoiceNumber() }}</span>
            </div>
          </div>
          <div class="invoice-date">
            <div class="date-label">{{ 'invoice.date' | translate }}</div>
            <div class="date-value">{{ invoice.session.appointment.startTime | date: 'dd/MM/yyyy' }}</div>
          </div>
        </div>

        <!-- Company and Patient Information -->
        <div class="header-info">
          <!-- Hospital/Company Information -->
          <div class="company-info">
            <h3>{{ currentUser.profile?.hospital?.name }}</h3>
            <div class="company-details">
              <div *ngIf="currentUser.profile?.hospital?.address" class="info-line">
                {{ currentUser.profile?.hospital?.address }}
              </div>
              <div *ngIf="currentUser.profile?.hospital?.phoneNumber" class="info-line">
                {{ currentUser.profile?.hospital?.phoneNumber }}
              </div>
              <!-- Doctor/Hospital email hidden as requested -->
              <!--
              <div *ngIf="currentUser.profile?.hospital?.email" class="info-line">
                <i class="icon-email"></i>
                {{ currentUser.profile?.hospital?.email }}
              </div>
              -->
            </div>
          </div>

          <!-- Patient Information -->
          <div class="patient-info">
            <h4>{{ 'invoice.patient' | translate }}</h4>
            <div class="patient-details">
              <div class="patient-name">
                {{ $any(appointment?.patient)?.lastName }} {{ $any(appointment?.patient)?.firstName }}
              </div>
              <div *ngIf="$any(appointment?.patient)?.email" class="patient-email">
                {{ $any(appointment?.patient)?.email }}
              </div>
            </div>
          </div>
        </div>
      </header>

      <!-- Minimal header when prescriptionHeader is disabled -->
      <header class="minimal-header" *ngIf="!currentUser?.profile?.hospital?.prescriptionHeader">
        <div class="minimal-spacing"></div>
        <div class="patient-section">
          <div class="patient-details">
            <p class="invoice-number-minimal">
              <span class="label">Facture N°:</span>
              {{ getInvoiceNumber() }}
            </p>
            <p class="patient-info">
              <span class="label">{{ 'invoice.patient' | translate }}:</span>
              {{ $any(appointment?.patient)?.lastName }} {{ $any(appointment?.patient)?.firstName }}
            </p>
            <p class="date-info">
              <span class="label">{{ 'invoice.date' | translate }}:</span>
              {{ invoice.session.appointment.startTime | date: 'dd/MM/yyyy' }}
            </p>
          </div>
        </div>
      </header>
      <!-- Modern Invoice Items Table -->
      <main class="invoice-main">
        <div class="items-section">
          <!-- <h3 class="section-title">{{ 'invoice.details' | translate }}</h3> -->

          <div class="items-table">
            <div class="table-header">
              <div class="col-description">{{ 'invoice.name' | translate }}</div>
              <div class="col-price">{{ 'invoice.price' | translate }}</div>
              <div class="col-quantity">{{ 'invoice.quantity' | translate }}</div>
              <div class="col-total">{{ 'invoice.total' | translate }}</div>
            </div>

            <div class="table-body">
              <div class="table-row" *ngFor="let item of invoice.items; let i = index">
                <div class="col-description">
                  <div class="item-name">{{ item.name }}</div>
                  <div class="item-description" *ngIf="item.description">{{ item.description }}</div>
                </div>
                <div class="col-price">
                  <span class="amount">{{ item.price | number:'1.2-2' }}</span>
                  <span class="currency">{{ getCurrency() }}</span>
                </div>
                <div class="col-quantity">
                  <span class="qty-value">{{ item.quantity }}</span>
                </div>
                <div class="col-total">
                  <span class="amount">{{ getItemTotal(item.price, item.quantity) }}</span>
                  <span class="currency">{{ getCurrency() }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Summary Section -->
          <div class="invoice-summary">
            <div class="summary-row subtotal" *ngIf="invoice.items && invoice.items.length > 1">
              <span class="summary-label">Sous-total</span>
              <span class="summary-value">
                {{ getTotal() }} {{ getCurrency() }}
              </span>
            </div>
            <div class="summary-row total">
              <span class="summary-label">{{ 'invoice.finalTotal' | translate }}</span>
              <span class="summary-value">
                {{ getTotal() }} {{ getCurrency() }}
              </span>
            </div>
          </div>
        </div>
      </main>
      <!-- Modern Footer - only when prescriptionHeader is enabled -->
      <footer class="modern-footer" *ngIf="currentUser?.profile?.hospital?.prescriptionHeader">
        <div class="footer-content">
          <div class="footer-left">
            <div class="thank-you">Merci pour votre confiance</div>
            <div class="footer-note" *ngIf="currentUser?.profile?.hospital?.address">
              {{ currentUser.profile?.hospital?.address }}
            </div>
          </div>
          <div class="footer-right">
            <div class="invoice-info">
              <div class="generated-date">
                Généré le {{ getCurrentDate() }}
              </div>
            </div>
          </div>
        </div>
      </footer>

      <!-- Minimal footer when prescriptionHeader is disabled -->
      <footer class="minimal-footer" *ngIf="!currentUser?.profile?.hospital?.prescriptionHeader">
      </footer>
    </div>
  </div>
</div>
