import {
  After<PERSON>iewInit,
  Component, ElementRef,
  EventEmitter, HostListener,
  Input,
  OnChanges,
  OnDestroy,
  OnInit,
  Output, Renderer2,
  SimpleChanges, ViewChild,
} from '@angular/core';
import {
  Appointment,
  getAppointmentBackgroundStylesClass,
} from '../../models/appointment.model';
import { AppointmentService } from '../../services/appointment.service';
import { ErrorService } from '../../services/error.service';
import { AppoitmentDialogComponent } from '../appoitment-dialog/appoitment-dialog.component';
import { MatDialog } from '@angular/material/dialog';
import {
  APPOINTMENT_STATES_OBJECT,
  CALLS_TYPES,
} from '../../constants/defaults.consts';
import { InvoiceComponent } from '../invoice/invoice.component';
import { exportAsPdf } from '../../utils/pdf.functions';
import { SocketService } from 'src/app/core/services/socket.service';
import { Direction } from '@angular/cdk/bidi';
import { StorageService } from 'src/app/core/services/storage.service';
import {fromEvent, Subscription} from 'rxjs';
import {Prescription} from '../../models/prescription.model';
import {PrescriptionPage} from '../../models/prescription-page.model';
import {ExportPrescriptionService} from '../../services/export-prescription.service';
import {SupplyService} from "../../services/supply.service";
export type PrescriptionType = 'ORDONNANCE' | 'RADIOLOGIE' | 'BIOLOGIE' | 'AUTRE';

@Component({
  selector: 'app-appointment',
  templateUrl: './appointment.component.html',
  styleUrls: ['./appointment.component.scss'],
  host: {
    '(document:click)': 'onClick($event)',
  },
})
export class AppointmentComponent implements OnInit
  , OnDestroy {

  smallScreen: boolean = false;
  smallWidthEnd = 1400;
  @HostListener('window:resize', ['$event'])
  onResize(event: any) {
    this.smallScreen = window.innerWidth < this.smallWidthEnd;
  }

  @ViewChild('printOptionsRef') printOptionsRef: ElementRef | undefined;
  @ViewChild('optionsRef') optionsRef: ElementRef | undefined;
  @ViewChild('printOptionsButtonRef') printOptionsButtonRef: ElementRef | undefined;
  @ViewChild('optionsButtonRef') optionsButtonRef: ElementRef | undefined;

  @Input() isFirstAppointment: boolean;
  @Input() appointment: Appointment;
  @Input() showUpdatedBackground: boolean;
  @Input() dir: Direction = 'ltr';
  @Output() appointmentUpdatedEvent = new EventEmitter<Appointment>();
  public getAppointmentBackgroundStylesClass = getAppointmentBackgroundStylesClass;
  public showOptions: boolean = false;
  public showPrintOptions: boolean = false;
  public statusIsUpdating: boolean = false;
  public delayTime: number | string = 0;
  public patientstateLoading: boolean;

  public options: {
    title: string;
    icon: string;
    click: any;
    type?: PrescriptionType;
    isLoading?: boolean;
    active?: boolean;
  }[] = [
    {
      title: 'appointments.options.edit',
      icon: 'edit',
      click: () => this.updateClick(this.appointment),
      active: true,
    },
    {
      title: 'appointments.options.makeFutureAppointment',
      icon: 'add',
      click: () => this.futurAppointmentDialog(),
      active: true,
    },
    {
      title: 'appointments.options.sendMessage',
      icon: 'email',
      click: () => {},
    },
    {
      title: 'appointments.options.downloadInvoice',
      icon: 'download',
      click: () => this.exportInvoicePDF(false),
      active: true,
    },
    {
      title: 'Ordonnances',
      icon: 'print',
      click: () => this.exportDownloadPrescriptionPDF(true, 3),
      isLoading: false,
      type: 'ORDONNANCE',
      active: false
    },
    {
      title: 'Radiologies',
      icon: 'print',
      click: () => this.exportDownloadPrescriptionPDF(true, 4),
      isLoading: false,
      type: 'RADIOLOGIE',
      active: false
    },
    {
      title: 'Biologies',
      icon: 'print',
      click: () => this.exportDownloadPrescriptionPDF(true, 5),
      isLoading: false,
      type: 'BIOLOGIE',
      active: false
    },
    {
      title: 'Autres',
      icon: 'print',
      click: () => this.exportDownloadPrescriptionPDF(true, 6),
      isLoading: false,
      type: 'AUTRE',
      active: false
    }
  ];
  public printOptions: {
    title: string;
    icon: string;
    click: any;
    isLoading: boolean;
    type?: PrescriptionType,
    active: boolean;
  }[] = [
    {
      title: 'appointments.options.printInvoice',
      icon: 'print',
      click: () => this.exportInvoicePDF(true),
      isLoading: false,
      active: true
    },
    {
      title: 'Ordonnances',
      icon: 'print',
      click: () => this.exportPrescriptionPDF(true, 1),
      isLoading: false,
      type: 'ORDONNANCE',
      active: false
    },
    {
      title: 'Radiologies',
      icon: 'print',
      click: () => this.exportPrescriptionPDF(true, 2),
      isLoading: false,
      type: 'RADIOLOGIE',
      active: false
    },
    {
      title: 'Biologies',
      icon: 'print',
      click: () => this.exportPrescriptionPDF(true, 3),
      isLoading: false,
      type: 'BIOLOGIE',
      active: false
    },
    {
      title: 'Autres',
      icon: 'print',
      click: () => this.exportPrescriptionPDF(true, 4),
      isLoading: false,
      type: 'AUTRE',
      active: false
    }
  ];

  public suppliesSuggestions: any[] = [];

  private getSuppliesSubscription: Subscription;



  constructor(
    private appointmentService: AppointmentService,
    private storageService: StorageService,
    private errorService: ErrorService,
    private dialog: MatDialog,
    private socketService: SocketService,
    private _eref: ElementRef,
    private renderer: Renderer2,
    private exportPrescriptionService: ExportPrescriptionService,
    private supplyService: SupplyService

  ) {
  }

  ngOnInit(): void {
    this.smallScreen = window.innerWidth < this.smallWidthEnd;

    this.initSocketListner();
    this.setDelayTime();
    this.updateOptions();
    this.updatePrintOptions();
    this.getSupplies('');

  }

  getSupplies(searchText: string) {
    if (this.getSuppliesSubscription) {
      this.getSuppliesSubscription.unsubscribe();
    }
    this.getSuppliesSubscription = this.supplyService
      .getSupplies(searchText, 1, 1000) // TODO: Remove pagination
      .subscribe((res: any) => {
        this.suppliesSuggestions = res.docs;
      });
  }



  setDelayTime = () => {
    if (this.appointment) {
      if (this.appointment.state === APPOINTMENT_STATES_OBJECT.approved) {
        setInterval(() => {
          this.updateDelayTimesForApproved();
        }, 1000);
      } else {
        if (
          !this.appointment.waitingTime ||
          this.appointment.state === APPOINTMENT_STATES_OBJECT.canceled
        ) {
          this.appointment.waitingTime = 0;
        }
        this.delayTime = this.appointment.waitingTime;
      }
    }
  };

  updateStatus = (status: string) => {
    if (status !== APPOINTMENT_STATES_OBJECT.almostCompleted) {
      this.statusIsUpdating = true;
    }
    if (status !== APPOINTMENT_STATES_OBJECT.almostCompleted) {
      this.appointmentService
        .updateAppointmentStatus(this.appointment._id as string, status)
        .subscribe(
          (res: any) => {
            this.appointment = res.appointment;
            if (this.appointment.state !== APPOINTMENT_STATES_OBJECT.approved) {
              if (
                !this.appointment.waitingTime ||
                this.appointment.state === APPOINTMENT_STATES_OBJECT.canceled
              ) {
                this.appointment.waitingTime = 0;
              }
              this.delayTime = this.appointment.waitingTime;
            } else {
              this.updateDelayTimesForApproved();
            }
            this.appointmentUpdatedEvent.emit(this.appointment);
          },
          (err) => this.errorService.handleError(err),
          () => {
            this.statusIsUpdating = false;
          }
        );
    } else {
      this.openInvoice();
    }
  };

  updateClick(appointment: Appointment) {
    const dialogRef = this.dialog.open(AppoitmentDialogComponent, {
      width: '600px',
      data: {
        type: CALLS_TYPES.update,
        appointment: JSON.parse(JSON.stringify(appointment)),
      },
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.appointmentUpdatedEvent.emit(result);
      }
    });
  }

  updateDelayTimesForApproved() {
    if (
      this.appointment.startTime &&
      this.appointment.state === APPOINTMENT_STATES_OBJECT.approved &&
      this.appointment.patientArrived
    ) {
      const timeDifference =
        new Date().getTime() -
        new Date(this.appointment.patientArrived).getTime();
      this.delayTime =
        timeDifference > 0
          ? parseInt((timeDifference / (1000 * 60)).toFixed(0), undefined)
          : 0;
    }
  }

  openInvoice() {
    const dialogRef = this.dialog.open(InvoiceComponent, {
      width: '800px',
      data: {
        invoice: this.appointment.invoice,
        suppliesSuggestions: this.suppliesSuggestions
      },
    });
    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.appointmentService
          .toCompleted(this.appointment._id as string)
          .subscribe(
            (res: any) => {
              this.appointment = res;
              this.appointmentUpdatedEvent.emit(this.appointment);
              this.getInvoice();
            },
            (err) => this.errorService.handleError(err),
            () => {
              this.statusIsUpdating = false;
            }
          );
      }
    });
  }

  isAlmostInvoiceReady() {
    return (
      [
        APPOINTMENT_STATES_OBJECT.almostCompleted,
        APPOINTMENT_STATES_OBJECT.completed,
      ].includes(this.appointment.state as string) &&
      this.appointment.invoice?._id
    );
  }

  isCanceled() {
    return (
      [
        APPOINTMENT_STATES_OBJECT.canceled
      ].includes(this.appointment.state as string)
    );
  }

  isNotInProgress() {
    return ![
      APPOINTMENT_STATES_OBJECT.inProgress,
      APPOINTMENT_STATES_OBJECT.completed,
      APPOINTMENT_STATES_OBJECT.almostCompleted,
      APPOINTMENT_STATES_OBJECT.canceled,
    ].includes(this.appointment.state as string);
  }

  toggleShowOptions() {
    this.showOptions = !this.showOptions;
    this.showPrintOptions = false;
  }

  toggleShowPrintOptions() {
    this.showPrintOptions = !this.showPrintOptions;
    this.showOptions = false;
  }

  exportInvoicePDF(print: boolean = false) {
    if (print) {
      this.printOptions[0].isLoading = true;
    } else {
      this.options[2].isLoading = true;
    }
    exportAsPdf(
      'invoice-container' + this.appointment._id,
      print,
      this.storageService.getCurrentLanguage()
    ).subscribe(() => {
      this.options[2].isLoading = false;
      this.printOptions[0].isLoading = false;
      this.showOptions = false;
      this.showPrintOptions = false;
    });
  }
  updateOptions() {
    if (this.appointment) {
      if ([
        APPOINTMENT_STATES_OBJECT.completed,
        APPOINTMENT_STATES_OBJECT.almostCompleted,
      ].includes(this.appointment.state as string)){
        this.options[0].active = false;
        this.options[1].active = true;
      } else {
        this.options[0].active = true;
        this.options[1].active = false;
      }
    }

  }
  updatePrintOptions() {
    const prescriptions: PrescriptionPage[] = this.appointment?.invoice?.session.prescriptions;
    if (prescriptions) {
      this.printOptions.forEach((printOption, index) => {
        if (index !== 0) {
          if (prescriptions.find(prescription => prescription.type === printOption.type)) {
            printOption.active = true;
          }
        }
      });
    }
    if (prescriptions) {
      this.options.forEach((printOption, index) => {
        if (![0, 1, 2].includes(index)) {
          if (prescriptions.find(prescription => prescription.type === printOption.type)) {
            printOption.active = true;
          }
        }
      });
    }

  }
  exportPrescriptionPDF(print: boolean = false, index: number) {
    this.printOptions[index].isLoading = true;

    const type = this.printOptions[index].type;
    const prescriptions = this.appointment
      .invoice?.session?.prescriptions?.filter((prescription: PrescriptionPage) => prescription.type === type);

    this.exportPrescriptionService.exportMultiplePrescriptions(prescriptions, this.appointment.invoice?.session, true).subscribe(() => {
      this.options[3].isLoading = false;
      this.printOptions[index].isLoading = false;
      this.showOptions = false;
    });
  }
  exportDownloadPrescriptionPDF(print: boolean = false, index: number) {

    this.printOptions[index].isLoading = true;

    const type = this.options[index].type;
    const prescriptions = this.appointment
      .invoice?.session?.prescriptions?.filter((prescription: PrescriptionPage) => prescription.type === type);

    this.exportPrescriptionService.exportMultiplePrescriptions(prescriptions, this.appointment.invoice?.session).subscribe(() => {
      this.options[index].isLoading = false;
      this.showOptions = false;
    });
  }

  toggleChecked(checked: boolean) {
    let patientArrived;
    this.patientstateLoading = true;
    if (checked) {
      patientArrived = new Date();
    }
    if (!checked) {
      patientArrived = null;
    }
    this.appointment.patientArrived = patientArrived;
    this.appointmentService
      .updateAppointment({
        _id: this.appointment._id as string,
        patientArrived: this.appointment.patientArrived,
      })
      .subscribe(
        (res: any) => {
          this.appointment = res.appointment;
          if (
            this.appointment.state === APPOINTMENT_STATES_OBJECT.approved &&
            this.appointment.patientArrived
          ) {
            this.updateDelayTimesForApproved();
          }
          this.appointmentUpdatedEvent.emit(this.appointment);
          this.patientstateLoading = false;
        },
        (err) => this.errorService.handleError(err),
        () => {
          this.statusIsUpdating = false;
          this.patientstateLoading = false;
        }
      );
  }
  getInvoice() {
    if (
      this.appointment.state === APPOINTMENT_STATES_OBJECT.completed ||
      this.appointment.state === APPOINTMENT_STATES_OBJECT.almostCompleted
    ) {
      this.appointmentService
        .getInvoice(this.appointment._id + '')
        .subscribe((res) => {
          this.appointment.invoice = res;
        });
    }
  }
  initSocketListner() {
    if (this.appointment._id) {
      this.socketService.listen(this.appointment._id).subscribe((res: any) => {
        if (res.appointment) {
          this.appointment = { ...this.appointment, ...res.appointment };
          this.appointmentUpdatedEvent.emit(this.appointment);
        }
      });
      this.getInvoice();
    }
  }

  onClick(event: any) {

    if (!this._eref?.nativeElement?.contains(event.target)) {
      // or some similar check
      this.showPrintOptions = false;
      this.showOptions = false;
    }
    if (!this.printOptionsButtonRef?.nativeElement?.contains(event.target) && !this.printOptionsRef?.nativeElement?.contains(event.target)) {
      // or some similar check
      this.showPrintOptions = false;
    }
    if (!this.optionsButtonRef?.nativeElement?.contains(event.target) && !this.optionsRef?.nativeElement?.contains(event.target)) {
      // or some similar check
      this.showOptions = false;
    }

  }

  futurAppointmentDialog() {
    this.dialog.open(AppoitmentDialogComponent, {
      width: '600px',
      data: {
        type: 'UPDATE',
        appointment: {patient: this.appointment.patient, doctor: this.appointment.doctor},
        fromSession: this.appointment.invoice?.session._id,
      },
    });
  }
  ngOnDestroy(): void {
    this.socketService.removeAllListeners(this.appointment._id + '');
  }
}
