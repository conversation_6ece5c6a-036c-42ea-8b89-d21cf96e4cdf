import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { AppointmentsRoutingModule } from './appointments-routing.module';
import { AppointmentsComponent } from './appointments.component';
import { SharedModule } from '../../../shared/shared.module';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatMenuModule } from '@angular/material/menu';
import { TranslateModule } from '@ngx-translate/core';
import { AppointmentsCalendarComponent } from './appointments-calendar/appointments-calendar.component';
import {FullCalendarModule} from "@fullcalendar/angular";
import { AppointmentCalendarItemComponent } from './appointment-calendar-item/appointment-calendar-item.component';

@NgModule({
  declarations: [AppointmentsComponent, AppointmentsCalendarComponent, AppointmentCalendarItemComponent],
  imports: [
    CommonModule,
    AppointmentsRoutingModule,
    SharedModule,
    MatSlideToggleModule,
    MatMenuModule,
    TranslateModule,
    FullCalendarModule

  ],
})
export class AppointmentsModule {}
