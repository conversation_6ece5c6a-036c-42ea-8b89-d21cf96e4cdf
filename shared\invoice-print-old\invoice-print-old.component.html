<div class="pdf-container" *ngIf="invoice">
  <div class="invoice-container" [id]="'invoice-container' + appointment?._id">
    <meta http-equiv="Content-Type" content="text/html;charset=UTF-8" />

    <div class="invoice-content">
      <header  *ngIf="!currentUser?.profile?.hospital?.prescriptionHeader" style="height: 150px">
        <div style="margin-top: 50px"></div>
        <div id="company" class="clearfix" style="display: none">
          <div>{{ currentUser.profile?.hospital?.name }}</div>
          <!--          <div style="width: 150px; white">{{currentUser.profile?.hospital?.address}}</div>-->
          <div>{{ currentUser.profile?.hospital?.phoneNumber }}</div>
          <div *ngIf="currentUser.profile?.hospital?.email">
            <a
              style="text-decoration: none"
              [href]="'mailto:' + currentUser.profile?.hospital?.email"
            >{{ currentUser.profile?.hospital?.email }}</a
            >
          </div>
        </div>
        <div id="project">
          <div>
            <span>{{ 'invoice.patient' | translate }}</span>
            {{
            $any(appointment?.patient)?.lastName +
            ' ' +
            $any(appointment?.patient)?.firstName
            }}
          </div>
          <div *ngIf="$any(appointment?.patient)?.email">
            <span>{{ 'invoice.email' | translate }}</span>
            <a [href]="'mailto:' + $any(appointment?.patient)?.email">{{
              $any(appointment?.patient)?.email
              }}</a>
          </div>
          <div>
            <span>{{ 'invoice.date' | translate }}</span
            >{{ invoice.session.appointment.startTime | date: 'dd/MM/yyyy' }}
          </div>
        </div>
      </header>
      <header class="clearfix" *ngIf="currentUser?.profile?.hospital?.prescriptionHeader">
        <div id="logo">
          <!--        <img src="logo.png">-->
        </div>
        <h1>{{ 'invoice.invoice' | translate }}</h1>
        <div id="company" class="clearfix">
          <div>{{ currentUser.profile?.hospital?.name }}</div>
          <!--          <div style="width: 150px; white">{{currentUser.profile?.hospital?.address}}</div>-->
          <div>{{ currentUser.profile?.hospital?.phoneNumber }}</div>
          <div *ngIf="currentUser.profile?.hospital?.email">
            <a
              style="text-decoration: none"
              [href]="'mailto:' + currentUser.profile?.hospital?.email"
              >{{ currentUser.profile?.hospital?.email }}</a
            >
          </div>
        </div>
        <div id="project">
          <div>
            <span>{{ 'invoice.patient' | translate }}</span>
            {{
              $any(appointment?.patient)?.lastName +
                ' ' +
              $any(appointment?.patient)?.firstName
            }}
          </div>
          <div *ngIf="$any(appointment?.patient)?.email">
            <span>{{ 'invoice.email' | translate }}</span>
            <a [href]="'mailto:' + $any(appointment?.patient)?.email">{{
              $any(appointment?.patient)?.email
            }}</a>
          </div>
          <div>
            <span>{{ 'invoice.date' | translate }}</span
            >{{ invoice.session.appointment.startTime | date: 'dd/MM/yyyy' }}
          </div>
        </div>
      </header>
      <main>
        <table>
          <thead>
            <tr>
              <th class="service">{{ 'invoice.name' | translate }}</th>
              <th>{{ 'invoice.price' | translate }}</th>
              <th>{{ 'invoice.quantity' | translate }}</th>
              <th>{{ 'invoice.total' | translate }}</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let item of invoice.items; let i = index">
              <td class="service">{{ item.name }}</td>
              <td class="unit">
                {{ item.price
                }}<span class="currency">{{
                  currentUser.profile?.hospital?.currency
                }}</span>
              </td>
              <td class="qty">{{ item.quantity }}</td>
              <td class="total">
                {{ getItemTotal(item.price, item.quantity)
                }}<span class="currency">{{
                  currentUser.profile?.hospital?.currency
                }}</span>
              </td>
            </tr>
            <tr>
              <td colspan="3" class="grand total">
                {{ 'invoice.finalTotal' | translate }}
              </td>
              <td class="grand total">
                {{ getTotal()
                }}<span class="currency">{{
                  currentUser.profile?.hospital?.currency
                }}</span>
              </td>
            </tr>
          </tbody>
        </table>
        <!--        <div id="notices">-->
        <!--          <div>NOTICE:</div>-->
        <!--          <div class="notice">Comment Here</div>-->
        <!--        </div>-->
      </main>
      <footer *ngIf="!currentUser?.profile?.hospital?.prescriptionHeader"></footer>
      <footer class="footer-line" *ngIf="currentUser?.profile?.hospital?.prescriptionHeader">
        <i
          >{{ 'invoice.adress' | translate }}:
          {{ currentUser.profile?.hospital?.address }}</i
        >
      </footer>
    </div>
  </div>
</div>