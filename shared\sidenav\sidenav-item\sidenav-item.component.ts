import { Component, Input, OnInit } from '@angular/core';
import { ThemePalette } from '@angular/material/core';
import { Direction } from '@angular/cdk/bidi';

@Component({
  selector: 'app-sidenav-item',
  templateUrl: './sidenav-item.component.html',
  styleUrls: ['./sidenav-item.component.scss'],
})
export class SidenavItemComponent implements OnInit {
  @Input() title = 'Item Name';
  @Input() icon: string;
  @Input() selected = false;
  @Input() collapsed = false;
  @Input() BadgeNumber: number = 0;
  @Input() BadgeColor: ThemePalette = 'warn';
  @Input() dir: Direction = 'ltr';

  public iconLink: string;

  constructor() {}

  ngOnInit(): void {
    this.setIconLink();
  }
  setIconLink() {
    if (this.icon) {
      this.iconLink = 'assets/icons/' + this.icon;
    } else {
      this.iconLink = 'assets/icons/home.png';
    }
  }
}
