<div
  [class]="
    'rend-as ' +
    (!isFirstAppointment ? 'mt-4 ' : '') +
    (showUpdatedBackground
      ? getAppointmentBackgroundStylesClass(appointment?.state)
      : '')
  "
  fxLayout="row"
  [id]="appointment._id"
>
  <!--  <div class="align-items-center ticket-container" fxFlex="4">-->
  <!--        <div fxLayout="column" fxLayoutAlign="center center" class="ticket-content">-->
  <!--          <small>Ticket</small>-->
  <!--          <p class="n-ticket">C001</p>-->
  <!--        </div>-->
  <!--  </div>-->
  <div [fxFlex]="smallScreen ? 20 : 25" [dir]="dir" class="ml-3" fxLayoutAlign="start center">
    <app-labeled-avatar
      [dir]="dir"
      mode="horizontal"
      [profile]="appointment.patient"
    ></app-labeled-avatar>
  </div>
  <app-labeled-avatar
    [dir]="dir"
    fxFlex="25"
    mode="horizontal"
    [profile]="appointment.doctor"
    [canEdit]="false"
    [label]="appointment.supply?.name | visitTypes"
  ></app-labeled-avatar>

  <div fxFlex>
    <h3 class="titre-time">{{ 'general.appointmentTime' | translate }}</h3>
    <h4 class="time-h">{{ appointment.startTime | date: 'HH:mm' }}</h4>
  </div>
  <div fxFlex>
    <div class="row align-items-center">
      <div class="col-lg-3 col-md-3">
        <button class="btn btn-action-icon btn-action-icon-edit option-as">
          <img
            src="assets/icons/edit-appointment.svg"
            alt="Edit"
            class="appointment-left-time"
          />
        </button>
      </div>
      <div class="col-lg-9 col-md-9">
        <h3 class="titre-time">{{ (smallScreen ? 'general.waitingTimesShort' : 'general.waitingTimes') | translate }}</h3>
        <h4 class="time-h" ng-trim="false">
          {{ delayTime | delayTimes }} {{ 'general.minuteShort' | translate }}
        </h4>
      </div>
    </div>
  </div>
  <div
    class="stat-rendez"
    fxFlex="25"
    fxLayout="row"
    fxLayoutAlign="space-around center"
  >
    <div fxFlex="10">
      <div
        fxLayout="column"
        fxLayoutAlign="center end"
        *ngIf="isNotInProgress()"
      >
        <mat-spinner diameter="20" *ngIf="patientstateLoading"></mat-spinner>
        <img
          *ngIf="appointment.patientArrived && !patientstateLoading"
          alt=""
          (click)="toggleChecked(false)"
          src="assets/icons/checkbox-checked.svg"
        />
        <img
          *ngIf="!appointment.patientArrived && !patientstateLoading"
          alt=""
          (click)="toggleChecked(true)"
          src="assets/icons/checkbox.svg"
        />
      </div>
    </div>
    <app-status-button
      [dir]="dir"
      [appointment]="appointment"
      (statusChangeEvent)="updateStatus($event)"
      [statusIsUpdating]="statusIsUpdating"
    ></app-status-button>
    <div class="btn-group">
      <app-circle-button
        *ngIf="isCanceled()"
        class="hidden"
        [matTooltip]="'appointments.tooltips.editAppointment' | translate"
      ></app-circle-button>
      <app-circle-button
        *ngIf="!isAlmostInvoiceReady() && !isNotInProgress() && !isCanceled()"
        name="add"
        [matTooltip]="'appointments.options.makeFutureAppointment' | translate"
        (click)="futurAppointmentDialog()"
      ></app-circle-button>
<!--      <app-circle-button-->
<!--        *ngIf="!isAlmostInvoiceReady()"-->
<!--        name="email"-->
<!--        [matTooltip]="'appointments.tooltips.sendMessage' | translate"-->
<!--      ></app-circle-button>-->
      <app-circle-button
        *ngIf="!isAlmostInvoiceReady() && !isCanceled()"
        name="edit"
        [matTooltip]="'appointments.tooltips.editAppointment' | translate"
        (click)="updateClick(appointment)"
      ></app-circle-button>

      <div #printOptionsButtonRef   fxLayoutAlign="center center"

      >
        <app-circle-button
          (click)="toggleShowPrintOptions()"
          *ngIf="isAlmostInvoiceReady()"
          name="print"
        ></app-circle-button>
      </div>
      <div #optionsButtonRef fxLayoutAlign="center center">
        <app-circle-button
          *ngIf="isAlmostInvoiceReady()"
          name="keyboard_arrow_down"
          (click)="toggleShowOptions()"
        ></app-circle-button>
      </div>

      <div #optionsRef>
        <div
          [dir]="dir"
          *ngIf="isAlmostInvoiceReady()"
          class="dropdown-menu dropdown-menu-right"
          [ngClass]="{ show: showOptions }"
        >
          <button
            *ngFor="let option of options; let i = index"
            class="dropdown-item normal-drop-item"
            (click)="option.click()"
            type="button"
            fxLayoutAlign="start center"

            [ngClass]="{'d-none': !option.active && ![2, 3].includes(i)}"
            [disabled]="!option.active"
          >
            <mat-spinner *ngIf="option.isLoading" diameter="15"></mat-spinner>
            <mat-icon *ngIf="!option.isLoading">{{ option.icon }}</mat-icon>
            {{ option.title | translate }}
          </button>
        </div>

      </div>
      <div #printOptionsRef
      >
        <div
          [dir]="dir"
          *ngIf="isAlmostInvoiceReady()"
          class="dropdown-menu dropdown-menu-right"
          [ngClass]="{ show: showPrintOptions }"
        >
          <button
            *ngFor="let option of printOptions; let i = index"
            class="dropdown-item normal-drop-item"
            (click)="option.click()"
            type="button"
            fxLayoutAlign="start center"
            [ngClass]="{'d-none': !option.active}"
             [disabled]="!option.active"
          >
            <mat-spinner *ngIf="option.isLoading" diameter="15"></mat-spinner>
            <mat-icon *ngIf="!option.isLoading">{{ option.icon }}</mat-icon>
            {{ option.title | translate }}
          </button>
        </div>

      </div>
    </div>
  </div>
</div>
<app-invoice-print
  *ngIf="
    appointment?.invoice && (options[2].isLoading || printOptions[0].isLoading)
  "
  [invoice]="appointment.invoice"
  [appointment]="appointment"
></app-invoice-print>
<app-prescription-print
  *ngIf="appointment?.invoice"
  [invoice]="appointment.invoice"
  [appointment]="appointment"
></app-prescription-print>
